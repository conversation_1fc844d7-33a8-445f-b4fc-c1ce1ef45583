#!/bin/bash

# AI聊天监控页面修复验证脚本

echo "=== AI聊天监控页面修复验证 ==="
echo "开始时间: $(date)"
echo ""

# 服务器地址
BASE_URL="http://localhost:8080"

# 企业ID
CORP_ID="ww5cfa32107e9a1f20"

echo "1. 测试服务器连接..."
if curl -s --connect-timeout 5 "$BASE_URL/monitor/data/test-json" > /dev/null; then
    echo "✓ 服务器连接正常"
else
    echo "✗ 服务器连接失败，请确保服务已启动"
    exit 1
fi

echo ""
echo "2. 生成测试数据..."

# 为精选客服企业ID生成数据
echo "为企业ID $CORP_ID 生成监控数据..."
GENERATE_RESULT=$(curl -s -X POST "$BASE_URL/monitor/data/generate/corp/$CORP_ID?days=7")
echo "生成结果: $GENERATE_RESULT"

# 等待数据生成完成
sleep 2

echo ""
echo "3. 验证数据生成..."

# 检查Redis数据
echo "检查Redis中的数据..."
CHECK_RESULT=$(curl -s "$BASE_URL/monitor/data/check-data")
echo "数据检查结果:"
echo "$CHECK_RESULT" | head -20
echo "..."

echo ""
echo "4. 测试AI数据查询..."
AI_DATA_RESULT=$(curl -s "$BASE_URL/monitor/data/test-ai-data")
echo "AI数据查询结果:"
echo "$AI_DATA_RESULT" | head -20
echo "..."

echo ""
echo "5. 测试监控概览数据..."
OVERVIEW_RESULT=$(curl -s "$BASE_URL/monitor/data/test-overview")
echo "概览数据查询结果:"
echo "$OVERVIEW_RESULT" | jq '.data.aiChat.syncSuccessRate' 2>/dev/null || echo "JSON解析失败或jq未安装"

echo ""
echo "6. 测试AI聊天监控API..."
AI_CHAT_API_RESULT=$(curl -s "$BASE_URL/monitor/api/ai-chat?corpId=$CORP_ID&days=3")
echo "AI聊天监控API结果:"
echo "$AI_CHAT_API_RESULT" | jq '.data.syncSuccessRate' 2>/dev/null || echo "JSON解析失败或jq未安装"

echo ""
echo "7. 验证页面访问..."
echo "请手动访问以下URL验证页面显示："
echo "- AI聊天监控: $BASE_URL/monitor/view/ai-chat?corpId=$CORP_ID&days=3"
echo "- 监控概览: $BASE_URL/monitor/view/overview?corpId=$CORP_ID&days=3"
echo "- 监控首页: $BASE_URL/monitor/view/"

echo ""
echo "8. 数据验证检查点..."

# 检查关键数据是否存在
echo "检查关键数据是否存在:"

# 检查同步调用数据
SYNC_SUCCESS_CHECK=$(echo "$AI_CHAT_API_RESULT" | jq '.data.syncSuccessData' 2>/dev/null)
if [ "$SYNC_SUCCESS_CHECK" != "null" ] && [ "$SYNC_SUCCESS_CHECK" != "" ]; then
    echo "✓ 同步调用成功数据存在"
else
    echo "✗ 同步调用成功数据缺失"
fi

# 检查异步调用数据
ASYNC_SUCCESS_CHECK=$(echo "$AI_CHAT_API_RESULT" | jq '.data.asyncSuccessData' 2>/dev/null)
if [ "$ASYNC_SUCCESS_CHECK" != "null" ] && [ "$ASYNC_SUCCESS_CHECK" != "" ]; then
    echo "✓ 异步调用成功数据存在"
else
    echo "✗ 异步调用成功数据缺失"
fi

# 检查AI结果类型数据
RESULT_TYPE_CHECK=$(echo "$AI_CHAT_API_RESULT" | jq '.data.resultTypeData' 2>/dev/null)
if [ "$RESULT_TYPE_CHECK" != "null" ] && [ "$RESULT_TYPE_CHECK" != "" ]; then
    echo "✓ AI结果类型数据存在"
else
    echo "✗ AI结果类型数据缺失"
fi

echo ""
echo "9. 生成其他企业ID的测试数据..."

# 为其他企业ID生成数据
for corp in "default_corp_1" "default_corp_2"; do
    echo "为企业ID $corp 生成数据..."
    curl -s -X POST "$BASE_URL/monitor/data/generate/corp/$corp?days=3" > /dev/null
done

echo ""
echo "=== 验证完成 ==="
echo "结束时间: $(date)"
echo ""
echo "如果页面仍然显示空白，请检查："
echo "1. 浏览器控制台是否有JavaScript错误"
echo "2. 网络请求是否正常返回数据"
echo "3. 企业ID参数是否正确传递"
echo "4. Redis服务是否正常运行"
echo ""
echo "调试建议："
echo "1. 打开浏览器开发者工具查看网络请求"
echo "2. 检查页面源码中的数据绑定"
echo "3. 验证后端日志是否有错误信息"
