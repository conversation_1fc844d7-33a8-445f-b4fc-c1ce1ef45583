<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.bj58.hy.wx</groupId>
    <artifactId>com.bj58.hy.wx.qywxbiz.parent</artifactId>
    <version>1.0.19</version>
    <packaging>pom</packaging>

    <modules>
        <module>contract</module>
        <module>service</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>

        <scf.dependencies.version>1.4.6</scf.dependencies.version>
        <spring-boot.version>2.2.11.RELEASE</spring-boot.version>

        <lib.version>1.0.0</lib.version>
    </properties>


    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bj58.hy.lib</groupId>
            <artifactId>common-lib</artifactId>
            <version>${lib.version}</version>
        </dependency>

        <dependency>
            <groupId>com.bj58.hy.lib</groupId>
            <artifactId>spring-lib</artifactId>
            <version>${lib.version}</version>
        </dependency>


        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>26.0.1</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <artifactId>scf-spring-boot-dependencies</artifactId>
                <groupId>com.bj58.spat</groupId>
                <version>${scf.dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>
            </plugins>

        </pluginManagement>
    </build>
</project>

