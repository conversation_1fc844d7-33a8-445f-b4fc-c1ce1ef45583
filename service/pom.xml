<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.bj58.hy.wx</groupId>
        <artifactId>com.bj58.hy.wx.qywxbiz.parent</artifactId>
        <version>1.0.21</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>com.bj58.hy.wx.qywxbiz.service</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <project.release.version>8.1.0</project.release.version>
        <cmcpc.contract.version>1.0.12</cmcpc.contract.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-log4j-2.x</artifactId>
            <version>${project.release.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-opentracing</artifactId>
            <version>${project.release.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-wtrace</artifactId>
            <version>8.1.1</version>
        </dependency>

        <dependency>
            <groupId>com.bj58.lbg.daojia</groupId>
            <artifactId>com.bj58.lbg.daojia.cscauth.contract</artifactId>
            <version>1.0.625</version>
        </dependency>

        <dependency>
            <groupId>com.bj58.hy.wx</groupId>
            <artifactId>com.bj58.hy.wx.qywx.contract</artifactId>
            <version>1.0.108</version>
        </dependency>

        <dependency>
            <groupId>com.bj58.hy.wx</groupId>
            <artifactId>com.bj58.hy.wx.qywxbiz.contract</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- aop lib -->
        <dependency>
            <groupId>com.bj58.hy.lib</groupId>
            <artifactId>spring-aspect-lib</artifactId>
            <version>${lib.version}</version>
        </dependency>

        <!-- jvm cache -->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

        <!-- guava -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.20</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>

        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
            <version>3.4.4</version>
        </dependency>

        <!-- db -->
        <dependency>
            <groupId>com.bj58.hy.lib</groupId>
            <artifactId>spring-jpa-lib</artifactId>
            <version>${lib.version}</version>
        </dependency>
        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-apt</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 这里引用fastjson单纯的是因为某些基础服务的jar里面有此依赖, 担心是provider的 =.= -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <!-- 58 SCF -->
        <dependency>
            <groupId>com.bj58.spat</groupId>
            <artifactId>scf-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Thymeleaf模板引擎 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions><!-- 去掉springboot默认配置 -->
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- bean validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- spring boot common lib -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- redis -->
        <dependency>
            <groupId>com.bj58.hy.lib</groupId>
            <artifactId>spring-redis-lib</artifactId>
            <version>${lib.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-actuator</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- wjob -->
        <dependency>
            <groupId>com.bj58.arch</groupId>
            <artifactId>wjob-spring-boot-starter</artifactId>
            <version>1.0.10</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- ESB消息 -->
        <dependency>
            <groupId>com.bj58.spat</groupId>
            <artifactId>com.bj58.spat.wmb.client</artifactId>
            <version>1.0.36</version>
        </dependency>

        <!-- 精选bcore -->
        <dependency>
            <groupId>com.bj58.hy.fx.bcore</groupId>
            <artifactId>com.bj58.hy.fx.bcore.contract</artifactId>
            <version>1.1.772</version>
            <exclusions>
                <exclusion>
                    <groupId>com.bj58.lbg.daojia.gateway</groupId>
                    <artifactId>com.bj58.lbg.daojia.gateway</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.bj58.spat</groupId>
                    <artifactId>com.bj58.spat.scf</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.bj58.spat</groupId>
                    <artifactId>com.bj58.spat.wmb.client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>easyexcel</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>com.bj58.lbg.commons.protocol</artifactId>
                    <groupId>com.bj58.lbg.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--订单中心-->
        <dependency>
            <groupId>com.bj58.lbg.business.platform</groupId>
            <artifactId>oms-contract</artifactId>
            <version>1.0.37</version>
        </dependency>

        <!-- 美事 -->
        <dependency>
            <groupId>com.bj58.meishi.openapi</groupId>
            <artifactId>com.bj58.meishi.openapi.client</artifactId>
            <version>1.0.24</version>
            <exclusions>
                <exclusion>
                    <groupId>com.bj58.spat</groupId>
                    <artifactId>com.bj58.spat.scf.client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <!-- qaSystem 算法知识库-->
        <dependency>
            <groupId>com.bj58.huangye.alg</groupId>
            <artifactId>com.bj58.huangye.alg.qasystem.client</artifactId>
            <version>1.0.121</version>
            <exclusions>
                <exclusion>
                    <groupId>com.bj58.spat</groupId>
                    <artifactId>com.bj58.spat.scf.client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.bj58.lbg.daojia.fxbanjia</groupId>
            <artifactId>com.bj58.lbg.daojia.fxbanjia.contract</artifactId>
            <version>1.2.09</version>
            <exclusions>
                <exclusion>
                    <groupId>com.bj58.spat</groupId>
                    <artifactId>com.bj58.spat.scf.server</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.bj58.spat</groupId>
                    <artifactId>com.bj58.spat.scf.client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.bj58.spat.tiresias</groupId>
                    <artifactId>com.bj58.spat.tiresias.scf.server.plugin</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.bj58.spat.tiresias</groupId>
                    <artifactId>com.bj58.spat.tiresias.scf.client.plugin</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.bj58.spat.tiresias</groupId>
                    <artifactId>com.bj58.spat.tiresias.interface</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.bj58.spat.tiresias</groupId>
                    <artifactId>com.bj58.spat.tiresias.client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.bj58.lbg.rs</groupId>
                    <artifactId>auth.service.contract</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.bj58.lbg.api</groupId>
            <artifactId>com.bj58.lbg.api.annotation</artifactId>
            <version>1.0.1</version>
        </dependency>

        <!-- CMCPC -->
        <dependency>
            <groupId>com.bj58.spat.cmc</groupId>
            <artifactId>com.bj58.spat.cmc.client-cache</artifactId>
            <version>${cmcpc.contract.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.bj58.spat</groupId>
                    <artifactId>com.bj58.spat.scf.client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>com.bj58.spat.dao</artifactId>
                    <groupId>com.bj58.spat</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.bj58.wmonitor</groupId>
            <artifactId>wmonitor-javaclient</artifactId>
            <version>1.0.24</version>
        </dependency>

        <!--     wconfig   -->
        <dependency>
            <groupId>com.bj58.wconfig</groupId>
            <artifactId>wconfig-client</artifactId>
            <version>1.0.10</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>2.20.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>2.20.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>2.20.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-jcl</artifactId>
                <version>2.20.0</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>jsr305</artifactId>
                <version>1.3.9</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>18.0</version>
            </dependency>
            <dependency>
                <groupId>com.bj58.dao.adapter</groupId>
                <artifactId>com.bj58.dao.adapter</artifactId>
                <version>1.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.bj58.spat</groupId>
                <artifactId>com.bj58.spat.memcachedclient</artifactId>
                <version>2.1.12</version>
            </dependency>

            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>1.9.4</version>
            </dependency>
            <dependency>
                <groupId>org.xerial.snappy</groupId>
                <artifactId>snappy-java</artifactId>
                <version>1.1.1.6</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.mysema.maven</groupId>
                <artifactId>apt-maven-plugin</artifactId>
                <version>1.1.3</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>process</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/generated-sources/java</outputDirectory>
                            <processor>com.querydsl.apt.jpa.JPAAnnotationProcessor</processor>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>com.querydsl</groupId>
                        <artifactId>querydsl-apt</artifactId>
                        <!--                        <version>${querydsl.version}</version>-->
                        <version>5.0.0</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>


</project>
