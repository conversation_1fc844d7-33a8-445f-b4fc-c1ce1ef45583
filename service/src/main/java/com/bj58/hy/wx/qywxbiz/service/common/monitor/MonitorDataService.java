package com.bj58.hy.wx.qywxbiz.service.common.monitor;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RKeys;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 监控数据查询服务
 * 从Redis中查询监控数据并进行统计分析
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class MonitorDataService {

    @Autowired
    private RedissonClient redisson;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 获取计数器数据
     */
    public long getCounterValue(String key, String date) {
        try {
            String fullKey = key + ":" + date;
            RAtomicLong atomicLong = redisson.getAtomicLong(fullKey);
            long value = atomicLong.get();
            log.debug("获取计数器数据: {} = {}", fullKey, value);
            return value;
        } catch (Exception e) {
            log.error("获取计数器数据失败: key={}, date={}", key, date, e);
            return 0L;
        }
    }

    /**
     * 获取最近N天的计数器数据
     */
    public Map<String, Long> getCounterDataForDays(String key, int days) {
        Map<String, Long> result = new LinkedHashMap<>();
        LocalDate today = LocalDate.now();
        
        for (int i = days - 1; i >= 0; i--) {
            LocalDate date = today.minusDays(i);
            String dateStr = date.format(DATE_FORMATTER);
            long value = getCounterValue(key, dateStr);
            result.put(dateStr, value);
        }
        
        return result;
    }

    /**
     * 获取详细记录数据
     */
    public List<String> getDetailRecords(String key, int limit) {
        try {
            RList<String> list = redisson.getList(key, StringCodec.INSTANCE);
            List<String> records = list.readAll();
            
            // 返回最新的记录
            if (records.size() > limit) {
                return records.subList(records.size() - limit, records.size());
            }
            
            return records;
        } catch (Exception e) {
            log.error("获取详细记录数据失败: key={}", key, e);
            return new ArrayList<>();
        }
    }

    /**
     * 计算成功率
     */
    public double calculateSuccessRate(String successKey, String failureKey, String date) {
        long successCount = getCounterValue(successKey, date);
        long failureCount = getCounterValue(failureKey, date);
        long total = successCount + failureCount;
        
        if (total == 0) {
            return 0.0;
        }
        
        return (double) successCount / total * 100;
    }

    /**
     * 计算最近N天的平均成功率
     */
    public double calculateAverageSuccessRate(String successKey, String failureKey, int days) {
        double totalRate = 0.0;
        int validDays = 0;
        
        LocalDate today = LocalDate.now();
        for (int i = days - 1; i >= 0; i--) {
            LocalDate date = today.minusDays(i);
            String dateStr = date.format(DATE_FORMATTER);
            
            long successCount = getCounterValue(successKey, dateStr);
            long failureCount = getCounterValue(failureKey, dateStr);
            long total = successCount + failureCount;
            
            if (total > 0) {
                double rate = (double) successCount / total * 100;
                totalRate += rate;
                validDays++;
            }
        }
        
        return validDays > 0 ? totalRate / validDays : 0.0;
    }

    /**
     * 获取欢迎语监控数据
     */
    public Map<String, Object> getWelcomeMonitorData(String corpId, String bizScene, int days) {
        Map<String, Object> data = new HashMap<>();
        
        // 生成成功率
        String generateSuccessKey = "welcome:generate:success:" + corpId + ":" + bizScene;
        String generateFailureKey = "welcome:generate:failure:" + corpId + ":" + bizScene;
        double generateSuccessRate = calculateAverageSuccessRate(generateSuccessKey, generateFailureKey, days);
        data.put("generateSuccessRate", generateSuccessRate);
        data.put("generateSuccessData", getCounterDataForDays(generateSuccessKey, days));
        data.put("generateFailureData", getCounterDataForDays(generateFailureKey, days));
        
        // 缓存命中率
        String cacheHitKey = "welcome:cache:hit";
        String cacheMissKey = "welcome:cache:miss";
        double cacheHitRate = calculateAverageSuccessRate(cacheHitKey, cacheMissKey, days);
        data.put("cacheHitRate", cacheHitRate);
        data.put("cacheHitData", getCounterDataForDays(cacheHitKey, days));
        data.put("cacheMissData", getCounterDataForDays(cacheMissKey, days));
        
        // 重试次数详情
        data.put("retryDetails", getDetailRecords("welcome:retry:count", 100));
        
        // 发送耗时详情
        data.put("durationDetails", getDetailRecords("welcome:send:duration", 100));
        
        return data;
    }

    /**
     * 获取AI聊天监控数据
     */
    public Map<String, Object> getAiChatMonitorData(String corpId, int days) {
        Map<String, Object> data = new HashMap<>();
        
        // 同步调用成功率
        String syncSuccessKey = "ai:call:success:" + corpId + ":sync";
        String syncFailureKey = "ai:call:failure:" + corpId + ":sync";
        double syncSuccessRate = calculateAverageSuccessRate(syncSuccessKey, syncFailureKey, days);
        data.put("syncSuccessRate", syncSuccessRate);
        data.put("syncSuccessData", getCounterDataForDays(syncSuccessKey, days));
        data.put("syncFailureData", getCounterDataForDays(syncFailureKey, days));
        
        // 异步调用成功率
        String asyncSuccessKey = "ai:call:success:" + corpId + ":async";
        String asyncFailureKey = "ai:call:failure:" + corpId + ":async";
        double asyncSuccessRate = calculateAverageSuccessRate(asyncSuccessKey, asyncFailureKey, days);
        data.put("asyncSuccessRate", asyncSuccessRate);
        data.put("asyncSuccessData", getCounterDataForDays(asyncSuccessKey, days));
        data.put("asyncFailureData", getCounterDataForDays(asyncFailureKey, days));
        
        // AI结果类型分布
        Map<String, Map<String, Long>> resultTypeData = new HashMap<>();
        for (int type = 0; type <= 5; type++) {
            String key = "ai:result:type:" + corpId + ":" + type;
            resultTypeData.put("type_" + type, getCounterDataForDays(key, days));
        }
        data.put("resultTypeData", resultTypeData);
        
        // 承接成功率
        String takeoverSuccessKey = "ai:takeover:success:" + corpId;
        String takeoverFailureKey = "ai:takeover:failure:" + corpId;
        double takeoverSuccessRate = calculateAverageSuccessRate(takeoverSuccessKey, takeoverFailureKey, days);
        data.put("takeoverSuccessRate", takeoverSuccessRate);
        
        // 响应时间详情
        data.put("responseTimeDetails", getDetailRecords("ai:response:time", 100));
        
        return data;
    }

    /**
     * 获取消息处理监控数据
     */
    public Map<String, Object> getMessageProcessMonitorData(String corpId, int days) {
        Map<String, Object> data = new HashMap<>();
        
        // 消息处理总量
        String userMessageKey = "message:total:" + corpId + ":EXTERNAL_USER";
        String botMessageKey = "message:total:" + corpId + ":USER";
        data.put("userMessageData", getCounterDataForDays(userMessageKey, days));
        data.put("botMessageData", getCounterDataForDays(botMessageKey, days));
        
        // 首次咨询承接率
        String firstConsultSuccessKey = "first:consult:success:" + corpId;
        String firstConsultFailureKey = "first:consult:failure:" + corpId;
        double firstConsultSuccessRate = calculateAverageSuccessRate(firstConsultSuccessKey, firstConsultFailureKey, days);
        data.put("firstConsultSuccessRate", firstConsultSuccessRate);
        data.put("firstConsultSuccessData", getCounterDataForDays(firstConsultSuccessKey, days));
        data.put("firstConsultFailureData", getCounterDataForDays(firstConsultFailureKey, days));
        
        // 人工介入数据
        String humanInterventionKey = "human:intervention:" + corpId;
        data.put("humanInterventionData", getCounterDataForDays(humanInterventionKey, days));
        
        // 处理延迟详情
        data.put("latencyDetails", getDetailRecords("message:latency", 100));
        
        return data;
    }

    /**
     * 获取用户满意度监控数据
     */
    public Map<String, Object> getUserSatisfactionMonitorData(String corpId, int days) {
        Map<String, Object> data = new HashMap<>();
        
        // 满意度调查发送数据
        String surveySentKey = "satisfaction:survey:sent:" + corpId;
        data.put("surveySentData", getCounterDataForDays(surveySentKey, days));
        
        // 满意度反馈数据
        String positiveKey = "satisfaction:positive:" + corpId;
        String negativeKey = "satisfaction:negative:" + corpId;
        double satisfactionRate = calculateAverageSuccessRate(positiveKey, negativeKey, days);
        data.put("satisfactionRate", satisfactionRate);
        data.put("positiveData", getCounterDataForDays(positiveKey, days));
        data.put("negativeData", getCounterDataForDays(negativeKey, days));
        
        // 反馈回收数据
        String feedbackRecoveryKey = "feedback:recovery:sent:" + corpId;
        data.put("feedbackRecoveryData", getCounterDataForDays(feedbackRecoveryKey, days));
        
        return data;
    }

    /**
     * 获取外部服务监控数据
     */
    public Map<String, Object> getExternalServiceMonitorData(String corpId, int days) {
        Map<String, Object> data = new HashMap<>();
        
        // Dify API调用成功率
        String difySuccessKey = "dify:api:success:" + corpId;
        String difyFailureKey = "dify:api:failure:" + corpId;
        double difySuccessRate = calculateAverageSuccessRate(difySuccessKey, difyFailureKey, days);
        data.put("difySuccessRate", difySuccessRate);
        data.put("difySuccessData", getCounterDataForDays(difySuccessKey, days));
        data.put("difyFailureData", getCounterDataForDays(difyFailureKey, days));
        
        // 订单查询成功率
        String[] orderTypes = {"jingxuan", "banjia"};
        Map<String, Object> orderQueryData = new HashMap<>();
        for (String orderType : orderTypes) {
            String successKey = "order:query:success:" + orderType;
            String failureKey = "order:query:failure:" + orderType;
            double successRate = calculateAverageSuccessRate(successKey, failureKey, days);
            
            Map<String, Object> typeData = new HashMap<>();
            typeData.put("successRate", successRate);
            typeData.put("successData", getCounterDataForDays(successKey, days));
            typeData.put("failureData", getCounterDataForDays(failureKey, days));
            
            orderQueryData.put(orderType, typeData);
        }
        data.put("orderQueryData", orderQueryData);
        
        return data;
    }

    /**
     * 获取监控概览数据
     */
    public Map<String, Object> getMonitorOverview(String corpId, int days) {
        Map<String, Object> overview = new HashMap<>();
        
        overview.put("welcome", getWelcomeMonitorData(corpId, "default", days));
        overview.put("aiChat", getAiChatMonitorData(corpId, days));
        overview.put("messageProcess", getMessageProcessMonitorData(corpId, days));
        overview.put("userSatisfaction", getUserSatisfactionMonitorData(corpId, days));
        overview.put("externalService", getExternalServiceMonitorData(corpId, days));
        
        return overview;
    }
}
