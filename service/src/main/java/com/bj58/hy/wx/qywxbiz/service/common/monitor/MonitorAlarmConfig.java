package com.bj58.hy.wx.qywxbiz.service.common.monitor;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 监控告警配置
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "monitor.alarm")
public class MonitorAlarmConfig {

    /**
     * 欢迎语监控告警阈值
     */
    private WelcomeAlarm welcome = new WelcomeAlarm();

    /**
     * AI聊天监控告警阈值
     */
    private AiChatAlarm aiChat = new AiChatAlarm();

    /**
     * 消息处理监控告警阈值
     */
    private MessageProcessAlarm messageProcess = new MessageProcessAlarm();

    /**
     * 用户满意度监控告警阈值
     */
    private UserSatisfactionAlarm userSatisfaction = new UserSatisfactionAlarm();

    /**
     * 外部服务监控告警阈值
     */
    private ExternalServiceAlarm externalService = new ExternalServiceAlarm();

    @Data
    public static class WelcomeAlarm {
        /**
         * 欢迎语生成成功率阈值（百分比）
         */
        private double generateSuccessRateThreshold = 95.0;

        /**
         * 缓存命中率阈值（百分比）
         */
        private double cacheHitRateThreshold = 80.0;

        /**
         * 平均重试次数阈值
         */
        private double avgRetryCountThreshold = 2.0;

        /**
         * 发送耗时P99阈值（毫秒）
         */
        private long sendDurationP99Threshold = 15000;
    }

    @Data
    public static class AiChatAlarm {
        /**
         * AI调用成功率阈值（百分比）
         */
        private double callSuccessRateThreshold = 90.0;

        /**
         * AI承接成功率阈值（百分比）
         */
        private double takeoverSuccessRateThreshold = 70.0;

        /**
         * 转人工率阈值（百分比）
         */
        private double transferToHumanRateThreshold = 30.0;

        /**
         * AI响应时间P99阈值（毫秒）
         */
        private long responseTimeP99Threshold = 20000;

        /**
         * 异步任务积压阈值
         */
        private int asyncTaskBacklogThreshold = 50;
    }

    @Data
    public static class MessageProcessAlarm {
        /**
         * 首次咨询承接率阈值（百分比）
         */
        private double firstConsultTakeoverRateThreshold = 85.0;

        /**
         * 消息处理延迟P95阈值（毫秒）
         */
        private long processLatencyP95Threshold = 25000;

        /**
         * 人工介入率阈值（百分比）
         */
        private double humanInterventionRateThreshold = 40.0;

        /**
         * 消息队列积压阈值
         */
        private int messageQueueBacklogThreshold = 1000;
    }

    @Data
    public static class UserSatisfactionAlarm {
        /**
         * 用户满意度阈值（百分比）
         */
        private double satisfactionRateThreshold = 60.0;

        /**
         * 满意度调查回复率阈值（百分比）
         */
        private double surveyResponseRateThreshold = 20.0;

        /**
         * 满意度调查最小样本数
         */
        private int surveyMinSampleSize = 10;
    }

    @Data
    public static class ExternalServiceAlarm {
        /**
         * Dify API调用成功率阈值（百分比）
         */
        private double difyApiSuccessRateThreshold = 95.0;

        /**
         * 订单查询成功率阈值（百分比）
         */
        private double orderQuerySuccessRateThreshold = 98.0;
    }
}
