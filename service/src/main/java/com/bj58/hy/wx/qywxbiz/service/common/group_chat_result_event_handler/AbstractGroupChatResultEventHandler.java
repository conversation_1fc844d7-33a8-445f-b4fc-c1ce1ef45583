package com.bj58.hy.wx.qywxbiz.service.common.group_chat_result_event_handler;

import com.alibaba.fastjson.JSONObject;
import lombok.NonNull;

/**
 * <AUTHOR>
 */
public abstract class AbstractGroupChatResultEventHandler {

    protected org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(this.getClass());

    public abstract void process(@NonNull final JSONObject event);

    public abstract boolean matched(@NonNull final JSONObject event);

}
