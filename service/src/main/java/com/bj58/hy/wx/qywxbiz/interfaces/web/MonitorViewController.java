package com.bj58.hy.wx.qywxbiz.interfaces.web;

import com.bj58.hy.wx.qywxbiz.service.common.monitor.MonitorDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * 监控页面控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping("/monitor/view")
public class MonitorViewController {

    @Autowired
    private MonitorDataService monitorDataService;

    /**
     * 监控首页 - 直接跳转到统一仪表板
     */
    @GetMapping("/")
    public String index() {
        return "redirect:/monitor/view/dashboard?corpId=ww5cfa32107e9a1f20&days=3";
    }

    /**
     * 监控介绍页面
     */
    @GetMapping("/intro")
    public String intro() {
        return "monitor/index";
    }



    /**
     * 统一监控仪表板页面 - 苹果风格
     */
    @GetMapping("/dashboard")
    public String unifiedDashboard(
            @RequestParam(defaultValue = "ww5cfa32107e9a1f20") String corpId,
            @RequestParam(defaultValue = "3") int days,
            Model model) {

        try {
            // 获取所有监控数据
            Map<String, Object> overviewData = monitorDataService.getMonitorOverview(corpId, days);

            model.addAttribute("corpId", corpId);
            model.addAttribute("days", days);
            model.addAttribute("overviewData", overviewData);
            model.addAttribute("pageTitle", "监控仪表板");

            return "monitor/unified-dashboard";
        } catch (Exception e) {
            log.error("获取统一监控数据失败", e);
            model.addAttribute("error", "获取统一监控数据失败: " + e.getMessage());
            return "monitor/error";
        }
    }


}
