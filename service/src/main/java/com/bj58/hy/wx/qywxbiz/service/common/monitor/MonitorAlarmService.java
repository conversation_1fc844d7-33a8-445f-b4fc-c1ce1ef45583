package com.bj58.hy.wx.qywxbiz.service.common.monitor;

import com.bj58.hy.wx.qywxbiz.service.common.risk_rule.RiskAlarmComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 监控告警服务
 * 基于Redis存储的监控数据进行告警检查
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MonitorAlarmService {

    @Autowired
    private MonitorDataService monitorDataService;

    @Autowired
    private RiskAlarmComponent riskAlarmComponent;

    @Autowired
    private MonitorAlarmConfig alarmConfig;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final int DEFAULT_CHECK_DAYS = 1; // 默认检查最近1天的数据

    /**
     * 检查欢迎语相关指标并告警
     *
     * @param corpId 企业ID
     * @param bizScene 业务场景
     */
    public void checkWelcomeMessageAlarms(String corpId, String bizScene) {
        try {
            Map<String, Object> welcomeData = monitorDataService.getWelcomeMonitorData(corpId, bizScene, DEFAULT_CHECK_DAYS);

            // 检查欢迎语生成成功率
            Double generateSuccessRate = (Double) welcomeData.get("generateSuccessRate");
            if (generateSuccessRate != null && generateSuccessRate < alarmConfig.getWelcome().getGenerateSuccessRateThreshold()) {
                Map<String, String> content = new HashMap<>();
                content.put("告警类型", "欢迎语生成成功率过低");
                content.put("企业ID", corpId);
                content.put("业务场景", bizScene);
                content.put("当前成功率", String.format("%.2f%%", generateSuccessRate));
                content.put("告警阈值", "< " + alarmConfig.getWelcome().getGenerateSuccessRateThreshold() + "%");
                content.put("检查时间", LocalDate.now().format(DATE_FORMATTER));

                riskAlarmComponent.alarm("欢迎语生成成功率告警", content);
                log.warn("欢迎语生成成功率告警: corpId={}, bizScene={}, rate={}%", corpId, bizScene, generateSuccessRate);
            }

            // 检查Redis缓存命中率
            Double cacheHitRate = (Double) welcomeData.get("cacheHitRate");
            if (cacheHitRate != null && cacheHitRate < alarmConfig.getWelcome().getCacheHitRateThreshold()) {
                Map<String, String> content = new HashMap<>();
                content.put("告警类型", "欢迎语缓存命中率过低");
                content.put("企业ID", corpId);
                content.put("当前命中率", String.format("%.2f%%", cacheHitRate));
                content.put("告警阈值", "< " + alarmConfig.getWelcome().getCacheHitRateThreshold() + "%");
                content.put("检查时间", LocalDate.now().format(DATE_FORMATTER));

                riskAlarmComponent.alarm("欢迎语缓存命中率告警", content);
                log.warn("欢迎语缓存命中率告警: corpId={}, rate={}%", corpId, cacheHitRate);
            }

            // 检查重试次数分布
            checkWelcomeRetryCount(corpId, welcomeData);

        } catch (Exception e) {
            log.error("检查欢迎语告警失败: corpId={}, bizScene={}", corpId, bizScene, e);
        }
    }

    /**
     * 检查欢迎语重试次数
     */
    @SuppressWarnings("unchecked")
    private void checkWelcomeRetryCount(String corpId, Map<String, Object> welcomeData) {
        try {
            java.util.List<String> retryDetails = (java.util.List<String>) welcomeData.get("retryDetails");
            if (retryDetails != null && !retryDetails.isEmpty()) {
                // 计算平均重试次数
                double totalRetries = 0;
                int count = 0;

                for (String detail : retryDetails) {
                    String[] parts = detail.split("\\|");
                    if (parts.length >= 3) {
                        try {
                            int retryCount = Integer.parseInt(parts[2]);
                            totalRetries += retryCount;
                            count++;
                        } catch (NumberFormatException e) {
                            log.debug("解析重试次数失败: {}", detail);
                        }
                    }
                }

                if (count > 0) {
                    double avgRetries = totalRetries / count;
                    if (avgRetries > alarmConfig.getWelcome().getAvgRetryCountThreshold()) {
                        Map<String, String> content = new HashMap<>();
                        content.put("告警类型", "欢迎语平均重试次数过高");
                        content.put("企业ID", corpId);
                        content.put("平均重试次数", String.format("%.2f", avgRetries));
                        content.put("告警阈值", "> " + alarmConfig.getWelcome().getAvgRetryCountThreshold() + "次");
                        content.put("检查时间", LocalDate.now().format(DATE_FORMATTER));
                        content.put("样本数量", String.valueOf(count));

                        riskAlarmComponent.alarm("欢迎语重试次数告警", content);
                        log.warn("欢迎语重试次数告警: corpId={}, avgRetries={}", corpId, avgRetries);
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查欢迎语重试次数失败: corpId={}", corpId, e);
        }
    }

    /**
     * 检查AI聊天相关指标并告警
     *
     * @param corpId 企业ID
     */
    public void checkAiChatAlarms(String corpId) {
        try {
            Map<String, Object> aiChatData = monitorDataService.getAiChatMonitorData(corpId, DEFAULT_CHECK_DAYS);

            // 检查AI同步调用成功率
            Double syncSuccessRate = (Double) aiChatData.get("syncSuccessRate");
            if (syncSuccessRate != null && syncSuccessRate < alarmConfig.getAiChat().getCallSuccessRateThreshold()) {
                Map<String, String> content = new HashMap<>();
                content.put("告警类型", "AI同步调用成功率过低");
                content.put("企业ID", corpId);
                content.put("当前成功率", String.format("%.2f%%", syncSuccessRate));
                content.put("告警阈值", "< " + alarmConfig.getAiChat().getCallSuccessRateThreshold() + "%");
                content.put("检查时间", LocalDate.now().format(DATE_FORMATTER));

                riskAlarmComponent.alarm("AI调用成功率告警", content);
                log.warn("AI同步调用成功率告警: corpId={}, rate={}%", corpId, syncSuccessRate);
            }

            // 检查AI异步调用成功率
            Double asyncSuccessRate = (Double) aiChatData.get("asyncSuccessRate");
            if (asyncSuccessRate != null && asyncSuccessRate < alarmConfig.getAiChat().getCallSuccessRateThreshold()) {
                Map<String, String> content = new HashMap<>();
                content.put("告警类型", "AI异步调用成功率过低");
                content.put("企业ID", corpId);
                content.put("当前成功率", String.format("%.2f%%", asyncSuccessRate));
                content.put("告警阈值", "< " + alarmConfig.getAiChat().getCallSuccessRateThreshold() + "%");
                content.put("检查时间", LocalDate.now().format(DATE_FORMATTER));

                riskAlarmComponent.alarm("AI调用成功率告警", content);
                log.warn("AI异步调用成功率告警: corpId={}, rate={}%", corpId, asyncSuccessRate);
            }

            // 检查AI承接成功率
            Double takeoverSuccessRate = (Double) aiChatData.get("takeoverSuccessRate");
            if (takeoverSuccessRate != null && takeoverSuccessRate < alarmConfig.getAiChat().getTakeoverSuccessRateThreshold()) {
                Map<String, String> content = new HashMap<>();
                content.put("告警类型", "AI承接成功率过低");
                content.put("企业ID", corpId);
                content.put("当前承接率", String.format("%.2f%%", takeoverSuccessRate));
                content.put("告警阈值", "< " + alarmConfig.getAiChat().getTakeoverSuccessRateThreshold() + "%");
                content.put("检查时间", LocalDate.now().format(DATE_FORMATTER));

                riskAlarmComponent.alarm("AI承接率告警", content);
                log.warn("AI承接成功率告警: corpId={}, rate={}%", corpId, takeoverSuccessRate);
            }

            // 检查转人工率（通过结果类型分布计算）
            checkAiTransferToHumanRate(corpId, aiChatData);

            // 检查AI响应时间
            checkAiResponseTime(corpId, aiChatData);

        } catch (Exception e) {
            log.error("检查AI聊天告警失败: corpId={}", corpId, e);
        }
    }

    /**
     * 检查AI转人工率
     */
    @SuppressWarnings("unchecked")
    private void checkAiTransferToHumanRate(String corpId, Map<String, Object> aiChatData) {
        try {
            Map<String, Map<String, Long>> resultTypeData = (Map<String, Map<String, Long>>) aiChatData.get("resultTypeData");
            if (resultTypeData != null) {
                // 计算转人工率：(type_1 + type_5) / total
                long transferCount = 0;
                long totalCount = 0;

                for (Map.Entry<String, Map<String, Long>> entry : resultTypeData.entrySet()) {
                    String type = entry.getKey();
                    Map<String, Long> typeData = entry.getValue();
                    long count = typeData.values().stream().mapToLong(Long::longValue).sum();

                    totalCount += count;
                    if ("type_1".equals(type) || "type_5".equals(type)) {
                        transferCount += count;
                    }
                }

                if (totalCount > 0) {
                    double transferRate = (double) transferCount / totalCount * 100;
                    if (transferRate > alarmConfig.getAiChat().getTransferToHumanRateThreshold()) {
                        Map<String, String> content = new HashMap<>();
                        content.put("告警类型", "AI转人工率过高");
                        content.put("企业ID", corpId);
                        content.put("当前转人工率", String.format("%.2f%%", transferRate));
                        content.put("告警阈值", "> " + alarmConfig.getAiChat().getTransferToHumanRateThreshold() + "%");
                        content.put("转人工次数", String.valueOf(transferCount));
                        content.put("总处理次数", String.valueOf(totalCount));
                        content.put("检查时间", LocalDate.now().format(DATE_FORMATTER));

                        riskAlarmComponent.alarm("AI转人工率告警", content);
                        log.warn("AI转人工率告警: corpId={}, rate={}%, transfer={}, total={}",
                            corpId, transferRate, transferCount, totalCount);
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查AI转人工率失败: corpId={}", corpId, e);
        }
    }

    /**
     * 检查AI响应时间
     */
    @SuppressWarnings("unchecked")
    private void checkAiResponseTime(String corpId, Map<String, Object> aiChatData) {
        try {
            java.util.List<String> responseTimeDetails = (java.util.List<String>) aiChatData.get("responseTimeDetails");
            if (responseTimeDetails != null && !responseTimeDetails.isEmpty()) {
                java.util.List<Long> responseTimes = new java.util.ArrayList<>();

                for (String detail : responseTimeDetails) {
                    String[] parts = detail.split("\\|");
                    if (parts.length >= 4) {
                        try {
                            long responseTime = Long.parseLong(parts[3]);
                            responseTimes.add(responseTime);
                        } catch (NumberFormatException e) {
                            log.debug("解析响应时间失败: {}", detail);
                        }
                    }
                }

                if (!responseTimes.isEmpty()) {
                    // 计算P99响应时间
                    responseTimes.sort(Long::compareTo);
                    int p99Index = (int) Math.ceil(responseTimes.size() * 0.99) - 1;
                    p99Index = Math.max(0, Math.min(p99Index, responseTimes.size() - 1));
                    long p99ResponseTime = responseTimes.get(p99Index);

                    if (p99ResponseTime > alarmConfig.getAiChat().getResponseTimeP99Threshold()) {
                        Map<String, String> content = new HashMap<>();
                        content.put("告警类型", "AI响应时间P99过高");
                        content.put("企业ID", corpId);
                        content.put("P99响应时间", p99ResponseTime + "ms");
                        content.put("告警阈值", "> " + (alarmConfig.getAiChat().getResponseTimeP99Threshold() / 1000) + "s");
                        content.put("样本数量", String.valueOf(responseTimes.size()));
                        content.put("检查时间", LocalDate.now().format(DATE_FORMATTER));

                        riskAlarmComponent.alarm("AI响应时间告警", content);
                        log.warn("AI响应时间告警: corpId={}, p99={}ms", corpId, p99ResponseTime);
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查AI响应时间失败: corpId={}", corpId, e);
        }
    }

    /**
     * 检查消息处理相关指标并告警
     *
     * @param corpId 企业ID
     */
    public void checkMessageProcessAlarms(String corpId) {
        try {
            Map<String, Object> messageProcessData = monitorDataService.getMessageProcessMonitorData(corpId, DEFAULT_CHECK_DAYS);

            // 检查首次咨询承接率
            Double firstConsultSuccessRate = (Double) messageProcessData.get("firstConsultSuccessRate");
            if (firstConsultSuccessRate != null && firstConsultSuccessRate < alarmConfig.getMessageProcess().getFirstConsultTakeoverRateThreshold()) {
                Map<String, String> content = new HashMap<>();
                content.put("告警类型", "首次咨询承接率过低");
                content.put("企业ID", corpId);
                content.put("当前承接率", String.format("%.2f%%", firstConsultSuccessRate));
                content.put("告警阈值", "< " + alarmConfig.getMessageProcess().getFirstConsultTakeoverRateThreshold() + "%");
                content.put("检查时间", LocalDate.now().format(DATE_FORMATTER));

                riskAlarmComponent.alarm("首次咨询承接率告警", content);
                log.warn("首次咨询承接率告警: corpId={}, rate={}%", corpId, firstConsultSuccessRate);
            }

            // 检查消息处理延迟
            checkMessageProcessLatency(corpId, messageProcessData);

            // 检查人工介入率
            checkHumanInterventionRate(corpId, messageProcessData);

        } catch (Exception e) {
            log.error("检查消息处理告警失败: corpId={}", corpId, e);
        }
    }

    /**
     * 检查消息处理延迟
     */
    @SuppressWarnings("unchecked")
    private void checkMessageProcessLatency(String corpId, Map<String, Object> messageProcessData) {
        try {
            java.util.List<String> latencyDetails = (java.util.List<String>) messageProcessData.get("latencyDetails");
            if (latencyDetails != null && !latencyDetails.isEmpty()) {
                java.util.List<Long> latencies = new java.util.ArrayList<>();

                for (String detail : latencyDetails) {
                    String[] parts = detail.split("\\|");
                    if (parts.length >= 4) {
                        try {
                            long latency = Long.parseLong(parts[3]);
                            latencies.add(latency);
                        } catch (NumberFormatException e) {
                            log.debug("解析处理延迟失败: {}", detail);
                        }
                    }
                }

                if (!latencies.isEmpty()) {
                    // 计算P95延迟
                    latencies.sort(Long::compareTo);
                    int p95Index = (int) Math.ceil(latencies.size() * 0.95) - 1;
                    p95Index = Math.max(0, Math.min(p95Index, latencies.size() - 1));
                    long p95Latency = latencies.get(p95Index);

                    if (p95Latency > alarmConfig.getMessageProcess().getProcessLatencyP95Threshold()) {
                        Map<String, String> content = new HashMap<>();
                        content.put("告警类型", "消息处理延迟P95过高");
                        content.put("企业ID", corpId);
                        content.put("P95延迟", p95Latency + "ms");
                        content.put("告警阈值", "> " + (alarmConfig.getMessageProcess().getProcessLatencyP95Threshold() / 1000) + "s");
                        content.put("样本数量", String.valueOf(latencies.size()));
                        content.put("检查时间", LocalDate.now().format(DATE_FORMATTER));

                        riskAlarmComponent.alarm("消息处理延迟告警", content);
                        log.warn("消息处理延迟告警: corpId={}, p95={}ms", corpId, p95Latency);
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查消息处理延迟失败: corpId={}", corpId, e);
        }
    }

    /**
     * 检查人工介入率
     */
    @SuppressWarnings("unchecked")
    private void checkHumanInterventionRate(String corpId, Map<String, Object> messageProcessData) {
        try {
            Map<String, Long> humanInterventionData = (Map<String, Long>) messageProcessData.get("humanInterventionData");
            Map<String, Long> userMessageData = (Map<String, Long>) messageProcessData.get("userMessageData");

            if (humanInterventionData != null && userMessageData != null) {
                long totalInterventions = humanInterventionData.values().stream().mapToLong(Long::longValue).sum();
                long totalUserMessages = userMessageData.values().stream().mapToLong(Long::longValue).sum();

                if (totalUserMessages > 0) {
                    double interventionRate = (double) totalInterventions / totalUserMessages * 100;
                    if (interventionRate > alarmConfig.getMessageProcess().getHumanInterventionRateThreshold()) {
                        Map<String, String> content = new HashMap<>();
                        content.put("告警类型", "人工介入率过高");
                        content.put("企业ID", corpId);
                        content.put("当前介入率", String.format("%.2f%%", interventionRate));
                        content.put("告警阈值", "> " + alarmConfig.getMessageProcess().getHumanInterventionRateThreshold() + "%");
                        content.put("介入次数", String.valueOf(totalInterventions));
                        content.put("用户消息数", String.valueOf(totalUserMessages));
                        content.put("检查时间", LocalDate.now().format(DATE_FORMATTER));

                        riskAlarmComponent.alarm("人工介入率告警", content);
                        log.warn("人工介入率告警: corpId={}, rate={}%, interventions={}, messages={}",
                            corpId, interventionRate, totalInterventions, totalUserMessages);
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查人工介入率失败: corpId={}", corpId, e);
        }
    }

    /**
     * 检查用户满意度相关指标并告警
     *
     * @param corpId 企业ID
     */
    public void checkUserSatisfactionAlarms(String corpId) {
        try {
            Map<String, Object> userSatisfactionData = monitorDataService.getUserSatisfactionMonitorData(corpId, DEFAULT_CHECK_DAYS);

            // 检查用户满意度
            Double satisfactionRate = (Double) userSatisfactionData.get("satisfactionRate");
            if (satisfactionRate != null && satisfactionRate < alarmConfig.getUserSatisfaction().getSatisfactionRateThreshold()) {
                Map<String, String> content = new HashMap<>();
                content.put("告警类型", "用户满意度过低");
                content.put("企业ID", corpId);
                content.put("当前满意度", String.format("%.2f%%", satisfactionRate));
                content.put("告警阈值", "< " + alarmConfig.getUserSatisfaction().getSatisfactionRateThreshold() + "%");
                content.put("检查时间", LocalDate.now().format(DATE_FORMATTER));

                riskAlarmComponent.alarm("用户满意度告警", content);
                log.warn("用户满意度告警: corpId={}, rate={}%", corpId, satisfactionRate);
            }

            // 检查满意度调查发送情况
            checkSatisfactionSurveyActivity(corpId, userSatisfactionData);

        } catch (Exception e) {
            log.error("检查用户满意度告警失败: corpId={}", corpId, e);
        }
    }

    /**
     * 检查满意度调查活跃度
     */
    @SuppressWarnings("unchecked")
    private void checkSatisfactionSurveyActivity(String corpId, Map<String, Object> userSatisfactionData) {
        try {
            Map<String, Long> surveySentData = (Map<String, Long>) userSatisfactionData.get("surveySentData");
            Map<String, Long> positiveData = (Map<String, Long>) userSatisfactionData.get("positiveData");
            Map<String, Long> negativeData = (Map<String, Long>) userSatisfactionData.get("negativeData");

            if (surveySentData != null && positiveData != null && negativeData != null) {
                long totalSent = surveySentData.values().stream().mapToLong(Long::longValue).sum();
                long totalPositive = positiveData.values().stream().mapToLong(Long::longValue).sum();
                long totalNegative = negativeData.values().stream().mapToLong(Long::longValue).sum();
                long totalResponses = totalPositive + totalNegative;

                // 检查调查回复率
                if (totalSent > 0) {
                    double responseRate = (double) totalResponses / totalSent * 100;
                    if (responseRate < alarmConfig.getUserSatisfaction().getSurveyResponseRateThreshold() &&
                        totalSent > alarmConfig.getUserSatisfaction().getSurveyMinSampleSize()) {
                        Map<String, String> content = new HashMap<>();
                        content.put("告警类型", "满意度调查回复率过低");
                        content.put("企业ID", corpId);
                        content.put("回复率", String.format("%.2f%%", responseRate));
                        content.put("发送数量", String.valueOf(totalSent));
                        content.put("回复数量", String.valueOf(totalResponses));
                        content.put("检查时间", LocalDate.now().format(DATE_FORMATTER));

                        riskAlarmComponent.alarm("满意度调查回复率告警", content);
                        log.warn("满意度调查回复率告警: corpId={}, rate={}%, sent={}, responses={}",
                            corpId, responseRate, totalSent, totalResponses);
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查满意度调查活跃度失败: corpId={}", corpId, e);
        }
    }

    /**
     * 检查外部服务相关指标并告警
     * 
     * @param corpId 企业ID
     */
    public void checkExternalServiceAlarms(String corpId) {
        try {
            Map<String, Object> externalServiceData = monitorDataService.getExternalServiceMonitorData(corpId, DEFAULT_CHECK_DAYS);

            // 检查Dify API调用成功率
            Double difySuccessRate = (Double) externalServiceData.get("difySuccessRate");
            if (difySuccessRate != null && difySuccessRate < alarmConfig.getExternalService().getDifyApiSuccessRateThreshold()) {
                Map<String, String> content = new HashMap<>();
                content.put("告警类型", "Dify API调用成功率过低");
                content.put("企业ID", corpId);
                content.put("当前成功率", String.format("%.2f%%", difySuccessRate));
                content.put("告警阈值", "< " + alarmConfig.getExternalService().getDifyApiSuccessRateThreshold() + "%");
                content.put("检查时间", LocalDate.now().format(DATE_FORMATTER));

                riskAlarmComponent.alarm("Dify API调用成功率告警", content);
                log.warn("Dify API调用成功率告警: corpId={}, rate={}%", corpId, difySuccessRate);
            }

            // 检查订单查询成功率
            checkOrderQuerySuccessRate(corpId, externalServiceData);

        } catch (Exception e) {
            log.error("检查外部服务告警失败: corpId={}", corpId, e);
        }
    }

    /**
     * 检查订单查询成功率
     */
    @SuppressWarnings("unchecked")
    private void checkOrderQuerySuccessRate(String corpId, Map<String, Object> externalServiceData) {
        try {
            Map<String, Object> orderQueryData = (Map<String, Object>) externalServiceData.get("orderQueryData");
            if (orderQueryData != null) {
                String[] orderTypes = {"jingxuan", "banjia"};

                for (String orderType : orderTypes) {
                    Map<String, Object> typeData = (Map<String, Object>) orderQueryData.get(orderType);
                    if (typeData != null) {
                        Double successRate = (Double) typeData.get("successRate");
                        if (successRate != null && successRate < alarmConfig.getExternalService().getOrderQuerySuccessRateThreshold()) {
                            Map<String, Long> successData = (Map<String, Long>) typeData.get("successData");
                            Map<String, Long> failureData = (Map<String, Long>) typeData.get("failureData");

                            long totalSuccess = successData != null ? successData.values().stream().mapToLong(Long::longValue).sum() : 0;
                            long totalFailure = failureData != null ? failureData.values().stream().mapToLong(Long::longValue).sum() : 0;

                            Map<String, String> content = new HashMap<>();
                            content.put("告警类型", "订单查询成功率过低");
                            content.put("企业ID", corpId);
                            content.put("订单类型", orderType);
                            content.put("当前成功率", String.format("%.2f%%", successRate));
                            content.put("告警阈值", "< " + alarmConfig.getExternalService().getOrderQuerySuccessRateThreshold() + "%");
                            content.put("成功次数", String.valueOf(totalSuccess));
                            content.put("失败次数", String.valueOf(totalFailure));
                            content.put("检查时间", LocalDate.now().format(DATE_FORMATTER));

                            riskAlarmComponent.alarm("订单查询成功率告警", content);
                            log.warn("订单查询成功率告警: corpId={}, orderType={}, rate={}%, success={}, failure={}",
                                corpId, orderType, successRate, totalSuccess, totalFailure);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查订单查询成功率失败: corpId={}", corpId, e);
        }
    }

    /**
     * 执行所有监控检查
     *
     * @param corpId 企业ID
     */
    public void checkAllAlarms(String corpId) {
        try {
            log.info("开始执行监控告警检查: corpId={}", corpId);

            // 检查欢迎语监控
            checkWelcomeMessageAlarms(corpId, "default");

            // 检查AI聊天监控
            checkAiChatAlarms(corpId);

            // 检查消息处理监控
            checkMessageProcessAlarms(corpId);

            // 检查用户满意度监控
            checkUserSatisfactionAlarms(corpId);

            // 检查外部服务监控
            checkExternalServiceAlarms(corpId);

            log.info("监控告警检查完成: corpId={}", corpId);
        } catch (Exception e) {
            log.error("执行监控告警检查失败: corpId={}", corpId, e);
        }
    }

    /**
     * 检查指定业务场景的欢迎语监控
     *
     * @param corpId 企业ID
     * @param bizScenes 业务场景列表
     */
    public void checkWelcomeMessageAlarms(String corpId, String... bizScenes) {
        if (bizScenes == null || bizScenes.length == 0) {
            bizScenes = new String[]{"default"};
        }

        for (String bizScene : bizScenes) {
            checkWelcomeMessageAlarms(corpId, bizScene);
        }
    }

    /**
     * 批量检查多个企业的监控告警
     *
     * @param corpIds 企业ID列表
     */
    public void checkAllAlarmsForCorps(String... corpIds) {
        if (corpIds == null || corpIds.length == 0) {
            log.warn("企业ID列表为空，跳过监控告警检查");
            return;
        }

        for (String corpId : corpIds) {
            try {
                checkAllAlarms(corpId);
            } catch (Exception e) {
                log.error("检查企业监控告警失败: corpId={}", corpId, e);
            }
        }
    }
}
