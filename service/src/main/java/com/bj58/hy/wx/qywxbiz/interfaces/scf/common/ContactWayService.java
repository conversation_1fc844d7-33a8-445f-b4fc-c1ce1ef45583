package com.bj58.hy.wx.qywxbiz.interfaces.scf.common;

import cn.hutool.core.thread.BlockPolicy;
import com.alibaba.fastjson.JSON;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.spring.support.aspect.GlobalExceptionWrapper;
import com.bj58.hy.lib.spring.support.aspect.VerifiedParams;
import com.bj58.hy.wx.qywx.contract.ICustomerAcquisitionService;
import com.bj58.hy.wx.qywx.contract.dto.corp_user.CorpUserDetailForThirdResp;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.AddContactWayReq;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.AddContactWayResp;
import com.bj58.hy.wx.qywxbiz.contract.IContactWayService;
import com.bj58.hy.wx.qywxbiz.contract.dto.contactway.*;
import com.bj58.hy.wx.qywxbiz.entity.AccountConfOperatorLogEntity;
import com.bj58.hy.wx.qywxbiz.entity.WxWorkContactWayBizAccountConfEntity;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ExternalContactRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.JuziApiRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.bo.IMMessageBo;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.enums.IMMessageLevelEnum;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.meishi.utils.IMMessageUtils;
import com.bj58.hy.wx.qywxbiz.repository.AccountConfOperatorLogRepository;
import com.bj58.hy.wx.qywxbiz.repository.WxWorkContactWayBizAccountConfRepository;
import com.bj58.hy.wx.qywxbiz.service.bo.BizLineEnum;
import com.bj58.hy.wx.qywxbiz.service.bo.BizSceneEnum;
import com.bj58.hy.wx.qywxbiz.service.common.contact_way.AbstractContactWayAccountStrategy;
import com.bj58.hy.wx.qywxbiz.service.common.contact_way.BindRelationContactWayAccountStrategy;
import com.bj58.hy.wx.qywxbiz.service.common.contact_way.bo.UserContactWayResult;
import com.bj58.hy.wx.qywxbiz.service.common.contact_way.risk_rule.CreateContractWayFreqRiskHandler;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.comp.LbgAiInterventionConditionsComponent;
import com.bj58.hy.wx.qywxbiz.utils.DateUtils;
import com.bj58.spat.scf.server.contract.annotation.ServiceBehavior;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.querydsl.core.QueryResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.LongCodec;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/11/19 11:24
 */
@Slf4j
@Component
@ServiceBehavior
public class ContactWayService implements IContactWayService {

    @SCFClient(lookup = ICustomerAcquisitionService.SCF_URL)
    private ICustomerAcquisitionService customerAcquisitionService;

    @Autowired
    private ObjectProvider<AbstractContactWayAccountStrategy> contactWayAccountHandlers;

    @Autowired
    protected WxWorkContactWayBizAccountConfRepository contactWayBizAccountConfRepository;

    @Autowired
    private ExternalContactRemoteService externalContactRemoteService;

    @Autowired
    private AccountConfOperatorLogRepository accountConfOperatorLogRepository;

    @Autowired
    private JuziApiRemoteService juziApiRemoteService;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private LbgAiInterventionConditionsComponent aiInterventionConditionsComponent;

    @Autowired
    private CreateContractWayFreqRiskHandler createContractWayFreqRiskHandler;

    @Autowired
    private BindRelationContactWayAccountStrategy bindRelationContactWayAccountStrategy;

    private final Executor executor = new ThreadPoolExecutor(
            0, 4,
            1, TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("contact_way_service_thread-%d").build(),
            new BlockPolicy(Runnable::run)
    );

    @Override
    @VerifiedParams
    @GlobalExceptionWrapper
    public Result<ContactWayResp> getContactWay(@NotNull @Valid final ContactWayReq req) {
        AbstractContactWayAccountStrategy handler = contactWayAccountHandlers.stream()
                .filter(p -> p.matched(req))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(handler)) {
            String errMsg = String.format("not found contact way account handler, biz line = %s, biz scene = %s",
                    req.getBizLine(), req.getBizScene());
            log.error(errMsg);
            return Result.failure(errMsg);
        }

        List<String> availableUserIds = handler.getUserIds(req);
        if (ObjectUtils.isEmpty(availableUserIds)) {
            return Result.failure("暂无可用账号，请联系相应负责人");
        }

        Result<AddContactWayResp> addContactWayRespResult = addContactWay(req, availableUserIds);

        // 尝试进行风控规则告警~
        createContractWayFreqRiskHandler.tryAlarm(req, availableUserIds);

        if (addContactWayRespResult.isSuccess()) {
            ContactWayResp contactWayResp = new ContactWayResp();
            contactWayResp.setQrCode(addContactWayRespResult.getData().getQrCode());
            contactWayResp.setUserId(availableUserIds.get(0));
            return Result.success(contactWayResp);
        }

        return Result.failure(addContactWayRespResult.getMsg());
    }

    @Override
    @VerifiedParams
    @GlobalExceptionWrapper
    public Result<Set<String>> getAvailableUserIds(@NotEmpty final String corpId,
                                                   @NotNull final Integer bizLine,
                                                   @NotEmpty final List<Integer> bizScenes) {

        List<WxWorkContactWayBizAccountConfEntity> entities = contactWayBizAccountConfRepository.getAvailableUser(
                bizLine, new HashSet<>(bizScenes),
                corpId, null);

        Set<String> corpUserIds = entities.stream()
                .map(WxWorkContactWayBizAccountConfEntity::getUserId)
                .collect(Collectors.toSet());

        if (bizScenes.contains(BizSceneEnum.精选_维修阿姨端入口加企微.getSceneId())
                || bizScenes.contains(BizSceneEnum.精选_保洁阿姨端入口加企微.getSceneId())) {
            corpUserIds.add("58-xiaoyang");
        }

        return Result.success(corpUserIds);
    }


    private Result<AddContactWayResp> addContactWay(@NotNull @Valid ContactWayReq req,
                                                    @NotNull List<String> userIds) {

        if (CollectionUtils.isEmpty(userIds)) {
            return Result.failure("not found any user id");
        }

        AddContactWayReq addContactWayReq = new AddContactWayReq();
        addContactWayReq.setCorpId(req.getCorpId());
        addContactWayReq.setType(1);
        addContactWayReq.setScene(2);
        addContactWayReq.setState(req.getState());
        addContactWayReq.setUser(userIds);
        addContactWayReq.setDelayDelete(true);
        addContactWayReq.setDelaySecond(60 * 60 * 3);

        try {
            Result<AddContactWayResp> addContactWayRespResult = customerAcquisitionService.addContactWay(addContactWayReq);

            log.info("生成【请联系我】成功，请求参数：{}, 响应结果：{}",
                    JSON.toJSONString(addContactWayReq),
                    JSON.toJSONString(addContactWayRespResult));
            return addContactWayRespResult;

        } catch (Exception e) {
            String errMsg = String.format("生成【请联系我】失败，请求参数：%s，错误信息：%s", JSON.toJSONString(addContactWayReq), e.getMessage());
            log.error(errMsg, e);
            return Result.failure(errMsg);
        }
    }

    @Override
    @VerifiedParams
    @GlobalExceptionWrapper
    public Result<UserListResp> listUsers(@NotNull final UserListReq req) {

        if (ObjectUtils.isNotEmpty(req.getBizScene())) {
            if (Objects.equals(req.getBizScene().get(0), -1)) {
                req.setBizLine(BizLineEnum.主站.getCode());
                req.setBizScene(Collections.singletonList(BizSceneEnum.主站_B端拉群.getSceneId()));
            } else if (Objects.equals(req.getBizScene().get(0), -2)) {
                req.setBizLine(BizLineEnum.主站.getCode());
                req.setBizScene(Collections.singletonList(BizSceneEnum.主站_C端拉群.getSceneId()));
            }
        }

        Date addCustomerStartTime = req.getAddCustomerStartTime();
        Date addCustomerEndTime = req.getAddCustomerEndTime();

        QueryResults<WxWorkContactWayBizAccountConfEntity> accountConfEntities = contactWayBizAccountConfRepository
                .listUserByParams(
                        req.getBizLine(), new HashSet<>(req.getBizScene()), req.getCorpId(),
                        req.getCityId(),
                        req.getCreateStartTime(), req.getCreateEndTime(),
                        req.getUpdateStartTime(), req.getUpdateEndTime(),
                        req.getState(),
                        req.getUserId(),
                        req.getPageNum(), req.getPageSize()
                );

        UserListResp resp = new UserListResp();
        resp.setTotalCount(accountConfEntities.getTotal());

        if (CollectionUtils.isEmpty(accountConfEntities.getResults())) {
            resp.setList(new ArrayList<>());
            return Result.success(resp);
        }

        Map<String, String> cityMap = getCityId2NameMap();

        resp.setList(accountConfEntities.getResults().parallelStream()
                .map(entity -> {
                    UserListResp.Item item = new UserListResp.Item();
                    item.setId(entity.getId());

                    item.setCityId(entity.getCityId());
                    List<String> cityNames = Arrays.stream(entity.getCityId().split(","))
                            .filter(StringUtils::isNotBlank)
                            .map(cityMap::get)
                            .collect(Collectors.toList());
                    if (entity.getCityId().equals(",0,")) {
                        if (entity.getBizScene().equals(BizSceneEnum.精选_支付成功订单详情页引导加企微.getSceneId())
                                || entity.getBizScene().equals(BizSceneEnum.精选_其他.getSceneId())) {
                            item.setCityName("");
                        } else {
                            item.setCityName("全国");
                        }

                    } else {
                        item.setCityName(String.join("、", cityNames));
                    }

                    item.setCreateTime(entity.getCreateTime());

                    item.setState(entity.getState());
                    item.setStateName(Objects.equals(entity.getState(), 1) ? "已上线" : "待上线");

                    item.setType(entity.getBizScene());
                    BizSceneEnum bizSceneEnum = BizSceneEnum.strictOf(entity.getBizLine(), entity.getBizScene());
                    if (Objects.equals(bizSceneEnum, BizSceneEnum.主站_B端拉群)) {
                        item.setType(-1);
                    } else if (Objects.equals(bizSceneEnum, BizSceneEnum.主站_C端拉群)) {
                        item.setType(-2);
                    }
                    item.setTypeName(bizSceneEnum.getSceneName());

                    item.setUpdateTime(entity.getUpdateTime());

                    item.setUserId(entity.getUserId());

                    RBucket<Long> addCustomerCountBucket = redisson.getBucket(String.format("%s:%s:TOTAL_CUSTOMER_COUNT",
                            entity.getCorpId(), entity.getUserId()), LongCodec.INSTANCE);
                    RBucket<Long> todayCustomerCountBucket = redisson.getBucket(String.format("%s:%s:TODAY_CUSTOMER_COUNT",
                            entity.getCorpId(), entity.getUserId()), LongCodec.INSTANCE);

                    if (!addCustomerCountBucket.isExists() || !todayCustomerCountBucket.isExists()) {
                        long addCustomerCount = externalContactRemoteService.countRelationByDate(
                                entity.getCorpId(), entity.getUserId(), addCustomerStartTime, addCustomerEndTime);
                        addCustomerCountBucket.set(addCustomerCount, 5, TimeUnit.MINUTES);

                        long todayAddCustomerCount = externalContactRemoteService.getCorpUserAddExternalUserIntradayCount(
                                entity.getCorpId(), entity.getUserId());
                        todayCustomerCountBucket.set(todayAddCustomerCount, 5, TimeUnit.MINUTES);
                    }

                    item.setAddCustomerCount(addCustomerCountBucket.get());
                    item.setTodayAddCustomerCount(todayCustomerCountBucket.get());

                    RBucket<String> userNameBucket = redisson.getBucket(String.format("%s:%s:USER_NAME",
                            entity.getCorpId(), entity.getUserId()), StringCodec.INSTANCE);
                    if (!userNameBucket.isExists()) {
                        CorpUserDetailForThirdResp userInfo = juziApiRemoteService.getUserInfoByUserId(entity.getCorpId(), entity.getUserId());
                        if (ObjectUtils.isNotEmpty(userInfo)) {
                            userNameBucket.set(userInfo.getBots().get(0).getName(), 1, TimeUnit.DAYS);
                        }
                    }
                    item.setUserName(userNameBucket.get());

                    item.setAiState(aiInterventionConditionsComponent.isAiUser(entity.getCorpId(), entity.getUserId()) ? 1 : 0);

                    return item;
                })
                .sorted((o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime()))
                .collect(Collectors.toList()));

        return Result.success(resp);
    }

    @Override
    @VerifiedParams
    @GlobalExceptionWrapper
    public Result<String> addUsers(@NotNull @Valid final AddUsersReq req) {

        if (Objects.equals(req.getBizScene(), -1)) {
            req.setBizLine(BizLineEnum.主站.getCode());
            req.setBizScene(BizSceneEnum.主站_B端拉群.getSceneId());
        } else if (Objects.equals(req.getBizScene(), -2)) {
            req.setBizLine(BizLineEnum.主站.getCode());
            req.setBizScene(BizSceneEnum.主站_C端拉群.getSceneId());
        }
        String corpId = req.getCorpId();
        List<String> userIds = req.getUserIds();
        Integer cityId = req.getCityId();
        Integer bizLine = req.getBizLine();
        Integer bizScene = req.getBizScene();

        Map<String, String> cityMap = getCityId2NameMap();
        String cityName = cityMap.get(String.valueOf(cityId));

        Date curDate = new Date();


        // 过滤重复项
        userIds = userIds.stream().distinct().collect(Collectors.toList());

        List<String> finalUserIds = userIds;
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            List<WxWorkContactWayBizAccountConfEntity> accountConfEntities =
                    contactWayBizAccountConfRepository.listUserByUserIds(bizLine, Sets.newHashSet(bizScene), corpId, Sets.newHashSet(finalUserIds));
            Map<String, WxWorkContactWayBizAccountConfEntity> accountMap = Maps.newHashMap();
            if (ObjectUtils.isNotEmpty(accountConfEntities)) {
                accountMap = accountConfEntities.stream()
                        .collect(Collectors.toMap(WxWorkContactWayBizAccountConfEntity::getUserId, o -> o));
            }

            for (String userId : finalUserIds) {
                WxWorkContactWayBizAccountConfEntity accountConf = accountMap.get(userId);
                if (ObjectUtils.isEmpty(accountConf)) {
                    accountConf = WxWorkContactWayBizAccountConfEntity.builder()
                            .corpId(corpId)
                            .bizLine(bizLine).bizScene(bizScene)
                            .userId(userId)
                            .rate(-1).rateInterval(1).rateIntervalUnit(1)
                            .createTime(curDate).updateTime(curDate)
                            .state(0)
                            .cityId("," + cityId + ",")
                            .build();


                    // 如果是支付页添加类型 默认是已上线状态
                    if (bizScene.equals(BizSceneEnum.精选_支付成功订单详情页引导加企微.getSceneId())
                            || bizScene.equals(BizSceneEnum.精选_其他.getSceneId())) {
                        accountConf.setState(1);
                    }

                    contactWayBizAccountConfRepository.save(accountConf);

                    AccountConfOperatorLogEntity log = new AccountConfOperatorLogEntity();
                    log.setOperator(req.getOperator());
                    log.setAccountConfId(accountConf.getId());

                    String operatorLog = DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss, curDate) + System.lineSeparator() +
                            req.getOperator() + "操作" + BizSceneEnum.strictOf(bizLine, bizScene).getSceneName() + System.lineSeparator();

                    if (!(bizScene.equals(BizSceneEnum.精选_支付成功订单详情页引导加企微.getSceneId())
                            || bizScene.equals(BizSceneEnum.精选_其他.getSceneId()))) {
                        operatorLog += "，城市：" + cityName + System.lineSeparator();
                    }

                    operatorLog += "账户：" + userId + "新建";

                    log.setLog(operatorLog);
                    log.setCreateTime(curDate);
                    log.setUpdateTime(curDate);
                    accountConfOperatorLogRepository.save(log);

                } else {
                    accountConf.setUpdateTime(curDate);
                    if (!StringUtils.containsIgnoreCase(accountConf.getCityId(), "," + cityId + ",")) {
                        accountConf.setCityId(accountConf.getCityId() + cityId + ",");

                        AccountConfOperatorLogEntity log = new AccountConfOperatorLogEntity();
                        log.setOperator(req.getOperator());
                        log.setAccountConfId(accountConf.getId());
                        log.setLog(DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss, curDate) + System.lineSeparator() +
                                req.getOperator() + "操作" + BizSceneEnum.strictOf(bizLine, bizScene).getSceneName() + System.lineSeparator() +
                                "，城市：" + cityName + System.lineSeparator() + "账户：" + userId + "新增了城市：" + cityName);
                        log.setCreateTime(curDate);
                        log.setUpdateTime(curDate);
                        accountConfOperatorLogRepository.save(log);
                    }

                    contactWayBizAccountConfRepository.save(accountConf);
                }
            }
        });

        return Result.success("success");
    }

    @Override
    @VerifiedParams
    @GlobalExceptionWrapper
    public Result<String> updateUserInfo(@NotNull @Valid final UpdateUserInfoReq req) {

        if (ObjectUtils.isNotEmpty(req.getBizScene())) {
            if (Objects.equals(req.getBizScene().get(0), -1)) {
                req.setBizLine(BizLineEnum.主站.getCode());
                req.setBizScene(Collections.singletonList(BizSceneEnum.主站_B端拉群.getSceneId()));
            } else if (Objects.equals(req.getBizScene().get(0), -2)) {
                req.setBizLine(BizLineEnum.主站.getCode());
                req.setBizScene(Collections.singletonList(BizSceneEnum.主站_C端拉群.getSceneId()));
            }
        }

        String corpId = req.getCorpId();
        String userId = req.getUserId();
        Integer bizLine = req.getBizLine();
        List<Integer> bizScene = req.getBizScene();
        Integer state = req.getState();
        String redCityIds = req.getCityIds();
        Long id = req.getId();
        Date curDate = new Date();
        Map<String, String> cityMap = getCityId2NameMap();
        // 原数据信息
        AtomicReference<Integer> beforeBizLine = new AtomicReference<>();
        AtomicReference<Integer> beforeBizScene = new AtomicReference<>();
        AtomicReference<Integer> beforeState = new AtomicReference<>();
        AtomicReference<String> beforeCityIds = new AtomicReference<>();

        Result<String> result = transactionTemplate.execute(status -> {
            Optional<WxWorkContactWayBizAccountConfEntity> accountConf =
                    contactWayBizAccountConfRepository.findById(id);
            if (!accountConf.isPresent()) {
                return Result.failure("not find user");
            }

            WxWorkContactWayBizAccountConfEntity accountConfEntity = accountConf.get();
            beforeBizLine.set(accountConfEntity.getBizLine());
            beforeBizScene.set(accountConfEntity.getBizScene());
            beforeState.set(accountConfEntity.getState());
            beforeCityIds.set(accountConfEntity.getCityId());

            String cityName = Arrays.stream(accountConfEntity.getCityId().split(","))
                    .filter(StringUtils::isNotEmpty)
                    .map(cityMap::get)
                    .collect(Collectors.joining("、"));

            String beforeUserId = accountConfEntity.getUserId();
            String newCityNames = "";

            // userId 不为空说明是编辑操作
            if (ObjectUtils.isNotEmpty(userId)) {
                List<WxWorkContactWayBizAccountConfEntity> countResult = contactWayBizAccountConfRepository.listUserByUserIds(
                        bizLine, Sets.newHashSet(accountConfEntity.getBizScene()), corpId, Sets.newHashSet(userId));
                if (ObjectUtils.isNotEmpty(countResult)) {

                    List<WxWorkContactWayBizAccountConfEntity> collect = countResult.stream()
                            .filter(entity -> !entity.getId().equals(accountConfEntity.getId())).collect(Collectors.toList());
                    if (!collect.isEmpty()) {
                        return Result.failure("已有此账户，请在原有账户上修改");
                    }
                }
            }

            // 修改城市
            if (ObjectUtils.isNotEmpty(redCityIds) && !Objects.equals(accountConfEntity.getCityId(), "," + redCityIds + ",")) {
                accountConfEntity.setCityId("," + redCityIds + ",");

                newCityNames = Arrays.stream(redCityIds.split(","))
                        .filter(StringUtils::isNotEmpty)
                        .map(cityMap::get)
                        .collect(Collectors.joining("、"));
            }

            // 修改成员
            if (ObjectUtils.isNotEmpty(userId) && !Objects.equals(accountConfEntity.getUserId(), userId)) {
                accountConfEntity.setUserId(userId);
            }

            accountConfEntity.setUpdateTime(curDate);
            contactWayBizAccountConfRepository.save(accountConfEntity);

            // 修改城市、企业账号 才会添加操作日志
            if (ObjectUtils.isNotEmpty(redCityIds) || ObjectUtils.isNotEmpty(userId)) {
                AccountConfOperatorLogEntity opeLog = new AccountConfOperatorLogEntity();
                opeLog.setOperator(req.getOperator());
                opeLog.setAccountConfId(accountConfEntity.getId());

                String logger = DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss, curDate) + System.lineSeparator() +
                        req.getOperator() + "操作" + BizSceneEnum.strictOf(accountConfEntity.getBizLine(), accountConfEntity.getBizScene()).getSceneName() + System.lineSeparator() +
                        "，城市：" + cityName + System.lineSeparator();

                if (StringUtils.isNotEmpty(newCityNames)) {
                    logger = logger + "修改城市为：" + newCityNames + System.lineSeparator();
                }

                logger = logger + "账户：" + beforeUserId + System.lineSeparator();

                if (StringUtils.isNotEmpty(beforeUserId) && !Objects.equals(beforeUserId, userId)) {
                    logger = logger + "修改了账户为：" + userId;
                }

                opeLog.setLog(logger);
                opeLog.setCreateTime(curDate);
                opeLog.setUpdateTime(curDate);
                accountConfOperatorLogRepository.save(opeLog);
            }


            // 上线状态 如果修改了城市 判断是否账号不足  不足发提示
            if (ObjectUtils.isNotEmpty(accountConfEntity.getState())
                    && Objects.equals(1, accountConfEntity.getState())
                    && ObjectUtils.isNotEmpty(redCityIds)
                    && !Objects.equals(beforeCityIds.get(), "," + redCityIds + ",")) {
                // 新旧城市去重拼一起
                String mergeList = Stream.of(Arrays.stream(redCityIds.split(",")).collect(Collectors.toList()),
                                Arrays.stream(beforeCityIds.get().split(",")).collect(Collectors.toList()))
                        .flatMap(List::stream)
                        .distinct()
                        .collect(Collectors.joining(","));
                if ("Product".equals(System.getenv("WCloud_Env"))) {
                    send(mergeList, bizLine, Sets.newHashSet(beforeBizScene.get()), corpId, cityMap, beforeBizLine.get(), beforeBizScene.get());
                }
            }


            if (ObjectUtils.isNotEmpty(state) && !Objects.equals(accountConfEntity.getState(), state)) {
                Integer curState = accountConfEntity.getState();
                String beforeStateName = Objects.equals(curState, 0) ? "待上线" : "已上线";
                String stateName = Objects.equals(state, 0) ? "待上线" : "已上线";
                accountConfEntity.setUpdateTime(curDate);
                accountConfEntity.setState(state);
                contactWayBizAccountConfRepository.save(accountConfEntity);
                AccountConfOperatorLogEntity log = new AccountConfOperatorLogEntity();
                log.setOperator(req.getOperator());
                log.setAccountConfId(accountConfEntity.getId());
                log.setLog(DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss, curDate) + System.lineSeparator() +
                        req.getOperator() + "操作" + BizSceneEnum.strictOf(accountConfEntity.getBizLine(), accountConfEntity.getBizScene()).getSceneName() + System.lineSeparator() +
                        "，城市：" + cityName + System.lineSeparator() + "账户：" + accountConfEntity.getUserId() + "由" + beforeStateName + "修改为" + stateName);
                log.setCreateTime(curDate);
                log.setUpdateTime(curDate);
                accountConfOperatorLogRepository.save(log);
            }

            return Result.success("success");
        });


        executor.execute(() -> {
            if (ObjectUtils.isNotEmpty(state) && !Objects.equals(beforeState.get(), state)) {
                if (Objects.equals(state, 0)) {
                    if ("Product".equals(System.getenv("WCloud_Env"))) {
                        send(beforeCityIds.get(), bizLine, Sets.newHashSet(beforeBizScene.get()), corpId, cityMap, beforeBizLine.get(), beforeBizScene.get());
                    }
                }
            }
        });

        return result;
    }

    @Override
    @VerifiedParams
    @GlobalExceptionWrapper
    public Result<OperatorLogListResp> listOperatorLog(@NotNull @Valid final Long id,
                                                       Integer pageNum,
                                                       Integer pageSize) {

        if (ObjectUtils.isEmpty(pageNum)) {
            pageNum = 1;
        }
        if (ObjectUtils.isEmpty(pageSize)) {
            pageSize = 10;
        }
        QueryResults<AccountConfOperatorLogEntity> page = accountConfOperatorLogRepository.listOperatorLog(id, pageNum, pageSize);
        OperatorLogListResp resp = new OperatorLogListResp();
        resp.setTotalCount(page.getTotal());
        resp.setList(page.getResults().stream()
                .map(entity -> {
                    OperatorLogListResp.Item item = new OperatorLogListResp.Item();
                    item.setOperatorName(entity.getOperator());
                    item.setOperatorTime(entity.getCreateTime());
                    item.setOperatorLog(entity.getLog());
                    item.setId(entity.getId());
                    return item;
                })
                .collect(Collectors.toList()));

        return Result.success(resp);
    }

    @Override
    @VerifiedParams
    @GlobalExceptionWrapper
    public Result<List<CityListResp>> listCity(String cityName) {
        // 获取业务线城市配置
        RBucket<String> cityBucket = redisson.getBucket(String.format("BIZ_LINE:%sBIZ_SCENE:%s:CITY_CONFIG_LIST",
                        BizLineEnum.精选.getCode(), BizSceneEnum.精选_保洁阿姨端入口加企微.getSceneId() + "_" + BizSceneEnum.精选_维修阿姨端入口加企微.getSceneId())
                , StringCodec.INSTANCE);
        String cityConf = cityBucket.get();
        List<CityListResp> allCities = JacksonUtils.parse(cityConf, new TypeReference<List<CityListResp>>() {
        });
        return Result.success(allCities);
    }


    private void send(String cityIds, Integer bizLine, Set<Integer> bizScene, String corpId, Map<String, String> cityMap, Integer beforeBizLine, Integer beforeBizScene) {
        for (String cityIdStr : cityIds.split(",")) {
            if (StringUtils.isBlank(cityIdStr)) {
                continue;
            }

            Integer cityId = Integer.valueOf(cityIdStr);

            QueryResults<WxWorkContactWayBizAccountConfEntity> countResult = contactWayBizAccountConfRepository.listUserByParams(
                    bizLine, bizScene, corpId, cityId,
                    null, null, null, null, 1, null
                    , 1, 1);

            if (countResult.getTotal() == 0) {
                String cityNameStr = cityMap.get(cityIdStr);
                this.sendNotEnoughNotice(cityNameStr, BizSceneEnum.strictOf(beforeBizLine, beforeBizScene).getSceneName());
            }
        }
    }

    private void sendNotEnoughNotice(String cityName, String bizSceneName) {

        List<String> oaNames = this.getOaNames();
        if (ObjectUtils.isEmpty(oaNames)) {
            return;
        }

        IMMessageBo imMessageBo = new IMMessageBo();
        imMessageBo.setTitle("企微账户不足通知");
        imMessageBo.setLevel(IMMessageLevelEnum.Warning);
        imMessageBo.setReceives(oaNames);

        IMMessageBo.TitleTextMap titleText = new IMMessageBo.TitleTextMap();
        titleText.put("消息内容", cityName + "的企微账户不足，请尽快添加企微托管账户");
        titleText.put("城市", cityName);
        titleText.put("业务线", bizSceneName);
        imMessageBo.setContent(titleText);

        IMMessageUtils.sendSimpleMessageToOas(imMessageBo);
    }

    @Override
    @VerifiedParams
    @GlobalExceptionWrapper
    public Result<String> updateUserStateFromCard(@NotEmpty final String operator,
                                                  @NotNull final Integer type,
                                                  @NotNull final Long userConfigId) {

        Map<String, String> cityMap = getCityId2NameMap();

        // 原数据信息
        AtomicReference<Integer> beforeBizLine = new AtomicReference<>();
        AtomicReference<Integer> beforeBizScene = new AtomicReference<>();
        AtomicReference<String> beforeCityIds = new AtomicReference<>();
        AtomicReference<String> corpId = new AtomicReference<>();

        Result<String> result = transactionTemplate.execute(status -> {
            Optional<WxWorkContactWayBizAccountConfEntity> accountConf = contactWayBizAccountConfRepository.findById(userConfigId);
            if (!accountConf.isPresent()) {
                return Result.failure("not find user");
            }

            WxWorkContactWayBizAccountConfEntity accountConfEntity = accountConf.get();
            beforeBizLine.set(accountConfEntity.getBizLine());
            beforeBizScene.set(accountConfEntity.getBizScene());
            beforeCityIds.set(accountConfEntity.getCityId());
            corpId.set(accountConfEntity.getCorpId());

            if (Objects.equals(type, 1)) {
                AccountConfOperatorLogEntity log = new AccountConfOperatorLogEntity();
                log.setOperator(operator);
                log.setAccountConfId(accountConfEntity.getId());
                log.setLog(DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss, new Date()) + System.lineSeparator() +
                        operator + "操作" + BizSceneEnum.strictOf(accountConfEntity.getBizLine(), accountConfEntity.getBizScene()).getSceneName() + System.lineSeparator() +
                        "账户：" + accountConfEntity.getUserId() + "企微账号风控通知 为拒绝");
                log.setCreateTime(new Date());
                log.setUpdateTime(new Date());
                accountConfOperatorLogRepository.save(log);

            } else if (Objects.equals(type, 2)) {
                if (Objects.equals(accountConfEntity.getState(), 0)) {
                    return Result.success("该账号已操作下线，请勿重复操作");
                }
                String userId = accountConfEntity.getUserId();
                List<WxWorkContactWayBizAccountConfEntity> accountConfEntities = contactWayBizAccountConfRepository.listUserByUserIds(accountConfEntity.getBizLine(),
                        Sets.newHashSet(BizSceneEnum.精选_维修阿姨端入口加企微.getSceneId(), BizSceneEnum.精选_保洁阿姨端入口加企微.getSceneId()),
                        accountConfEntity.getCorpId(), Sets.newHashSet(userId));
                for (WxWorkContactWayBizAccountConfEntity confEntity : accountConfEntities) {
                    if (Objects.equals(confEntity.getState(), 0)) {
                        continue;
                    }
                    int state = 0;
                    Date curDate = new Date();
                    Integer beforeState = confEntity.getState();
                    String beforeStateName = Objects.equals(beforeState, 0) ? "待上线" : "已上线";
                    String stateName = "待上线";
                    confEntity.setUpdateTime(curDate);
                    confEntity.setState(state);
                    contactWayBizAccountConfRepository.save(confEntity);
                    AccountConfOperatorLogEntity log = new AccountConfOperatorLogEntity();
                    log.setOperator(operator);
                    log.setAccountConfId(confEntity.getId());
                    log.setLog(DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss, curDate) + System.lineSeparator() +
                            operator + "操作" + BizSceneEnum.strictOf(confEntity.getBizLine(), confEntity.getBizScene()).getSceneName() + System.lineSeparator() +
                            System.lineSeparator() + "账户：" + confEntity.getUserId() + "通过 企微账号风控通知 由" + beforeStateName + "修改为" + stateName);
                    log.setCreateTime(curDate);
                    log.setUpdateTime(curDate);
                    accountConfOperatorLogRepository.save(log);
                    for (String cityIdStr : confEntity.getCityId().split(",")) {
                        if (StringUtils.isEmpty(cityIdStr)) {
                            continue;
                        }

                        Integer cityId = Integer.valueOf(cityIdStr);
                        QueryResults<WxWorkContactWayBizAccountConfEntity> countResult =
                                contactWayBizAccountConfRepository.listUserByParams(
                                        confEntity.getBizLine(), Sets.newHashSet(confEntity.getBizScene()),
                                        confEntity.getCorpId(), cityId, null, null, null, null, 1, null
                                        , 1, 1);
                        if (countResult.getTotal() == 0) {
                            String cityNameStr = cityMap.get(cityIdStr);
                            this.sendNotEnoughNotice(cityNameStr, BizSceneEnum.strictOf(confEntity.getBizLine(), confEntity.getBizScene()).getSceneName());
                        }
                    }
                }
            }
            return Result.success("操作成功");
        });

        executor.execute(() -> {
            if (Objects.equals(type, 2)) {
                for (String cityIdStr : beforeCityIds.get().split(",")) {
                    if (StringUtils.isBlank(cityIdStr)) {
                        continue;
                    }

                    Integer cityId = Integer.valueOf(cityIdStr);

                    QueryResults<WxWorkContactWayBizAccountConfEntity> countResult = contactWayBizAccountConfRepository.listUserByParams(
                            beforeBizLine.get(), Sets.newHashSet(beforeBizScene.get()), corpId.get(), cityId,
                            null, null, null, null, null, null
                            , 1, 1);

                    if (countResult.getTotal() == 0) {
                        String cityNameStr = cityMap.get(cityIdStr);
                        this.sendNotEnoughNotice(cityNameStr, BizSceneEnum.strictOf(beforeBizLine.get(), beforeBizScene.get()).getSceneName());
                    }
                }
            }
        });

        return result;
    }

    @Override
    @VerifiedParams
    @GlobalExceptionWrapper
    public Result<String> warningUserState(@NotEmpty final String corpId,
                                           @NotNull final Integer bizLine,
                                           @NotNull final Integer bizScenes,
                                           @NotEmpty final String userId) {

        List<WxWorkContactWayBizAccountConfEntity> accountConfEntities =
                contactWayBizAccountConfRepository.listUserByUserIds(bizLine, Sets.newHashSet(bizScenes), corpId, Sets.newHashSet(userId));

        if (ObjectUtils.isEmpty(accountConfEntities)) {
            return Result.success("");
        }
        WxWorkContactWayBizAccountConfEntity accountConfEntity = accountConfEntities.get(0);
        if (ObjectUtils.isEmpty(accountConfEntity) || Objects.equals(accountConfEntity.getState(), 0)) {
            return Result.success("");
        }

        // 半小时内，只推送1次风控上报卡片
        RBucket<String> bucket = redisson.getBucket(String.format("HYQYWX_ACCOUNT_ALARM_CARD_USER:%s", accountConfEntity.getUserId()), StringCodec.INSTANCE);
        String redisUserId = bucket.get();
        if (StringUtils.isNotEmpty(redisUserId)) {
            log.info(String.format("账号:%s,半小时内已经报过警,本次不再报警", redisUserId));
            return Result.success("");
        }
        List<String> oaNames = this.getOaNames();
        if (ObjectUtils.isEmpty(oaNames)) {
            return Result.failure("获取上报人失败");
        }
        Map<String, String> cityMap = this.getCityId2NameMap();
        String cityName = Arrays.stream(accountConfEntity.getCityId().split(","))
                .filter(StringUtils::isNotEmpty)
                .map(cityMap::get)
                .collect(Collectors.joining("、"));

        IMMessageBo imMessageBo = new IMMessageBo();
        imMessageBo.setTitle("企微账号风控通知");
        imMessageBo.setLevel(IMMessageLevelEnum.Warning);
        imMessageBo.setReceives(oaNames);

        IMMessageBo.TitleTextMap titleText = new IMMessageBo.TitleTextMap();
        titleText.put("消息内容", cityName + "的" + accountConfEntity.getUserId() + "被企微风控，请核实确认后操作下线");
        titleText.put("账号所属城市", cityName);
        titleText.put("业务线", BizSceneEnum.strictOf(accountConfEntity.getBizLine(), accountConfEntity.getBizScene()).getSceneName());
        CorpUserDetailForThirdResp userInfo = juziApiRemoteService.getUserInfoByUserId(corpId, accountConfEntity.getUserId());
        if (ObjectUtils.isNotEmpty(userInfo)) {
            titleText.put("小组名称", userInfo.getName());
            titleText.put("托管的账户", userInfo.getBots().get(0).getName());
        } else {
            titleText.put("托管的账户", "该账号托管异常请线下确认");
        }
        imMessageBo.setContent(titleText);

        IMMessageBo.ButtonMap button = new IMMessageBo.ButtonMap();
        button.put("拒绝", "https://csc.58corp.com/auntscf/updateUserStateFromCard?configId=" + accountConfEntity.getId() + "&type=1");
        button.put("下线", "https://csc.58corp.com/auntscf/updateUserStateFromCard?configId=" + accountConfEntity.getId() + "&type=2");
        imMessageBo.setButton(button);

        IMMessageUtils.sendSimpleMessageToOas(imMessageBo);
        bucket.set(accountConfEntity.getUserId(), 30, TimeUnit.MINUTES);

        return Result.success("");
    }

    @Override
    @VerifiedParams
    @GlobalExceptionWrapper
    public Result<ContactWayResp> getQrCodeById(@NotEmpty final String corpId,
                                                @NotNull final Long id) {

        Optional<WxWorkContactWayBizAccountConfEntity> accountConf = contactWayBizAccountConfRepository.findById(id);
        if (!accountConf.isPresent()) {
            return Result.failure("not find user");
        }
        WxWorkContactWayBizAccountConfEntity accountConfEntity = accountConf.get();

        ContactWayReq wayReq = new ContactWayReq();
        wayReq.setCorpId(corpId);
        wayReq.setBizLine(accountConfEntity.getBizLine());
        wayReq.setBizScene(accountConfEntity.getBizScene());
        wayReq.setState("1");
        Result<AddContactWayResp> addContactWayRespResult = this.addContactWay(wayReq, Collections.singletonList(accountConfEntity.getUserId()));
        if (addContactWayRespResult.isFailed()) {
            return Result.failure(addContactWayRespResult.getMsg());
        }
        ContactWayResp contactWayResp = new ContactWayResp();
        contactWayResp.setQrCode(addContactWayRespResult.getData().getQrCode());
        contactWayResp.setUserId(accountConfEntity.getUserId());
        return Result.success(contactWayResp);
    }

    @Override
    public Result<List<BizTypeResp>> getBizType() {

        List<BizTypeResp> bizTypeRespList = Lists.newArrayList();

        RMap<String, String> bizTypeMap;
        if ("Product".equals(System.getenv("WCloud_Env"))) {
            bizTypeMap = redisson.getMap(String.format("BIZ_LINE:%s:BIZ_TYPE_LIST", BizLineEnum.精选.getCode()), StringCodec.INSTANCE);
        } else {
            bizTypeMap = redisson.getMap(String.format("BIZ_LINE:%s:SANDBOX:BIZ_TYPE_LIST", BizLineEnum.精选.getCode()), StringCodec.INSTANCE);
        }

        if (com.bj58.hy.lib.core.util.ObjectUtils.notEmpty(bizTypeMap)) {
            for (final Map.Entry<String, String> next : bizTypeMap.entrySet()) {
                BizTypeResp build = BizTypeResp.builder()
                        .type(next.getKey())
                        .typeName(next.getValue()).build();

                bizTypeRespList.add(build);
            }
        }

        if ("Product".equals(System.getenv("WCloud_Env"))) {
            bizTypeMap = redisson.getMap(String.format("BIZ_LINE:%s:BIZ_TYPE_LIST", BizLineEnum.主站.getCode()), StringCodec.INSTANCE);
        } else {
            bizTypeMap = redisson.getMap(String.format("BIZ_LINE:%s:SANDBOX:BIZ_TYPE_LIST", BizLineEnum.主站.getCode()), StringCodec.INSTANCE);
        }

        if (com.bj58.hy.lib.core.util.ObjectUtils.notEmpty(bizTypeMap)) {
            for (final Map.Entry<String, String> next : bizTypeMap.entrySet()) {
                BizTypeResp build = BizTypeResp.builder()
                        .type(next.getKey())
                        .typeName(next.getValue()).build();

                bizTypeRespList.add(build);
            }
        }

        return Result.success(bizTypeRespList);
    }

    @Override
    public Result<String> deleteUser(Long id) {

        // 查询客服
        if (ObjectUtils.isEmpty(id)) {
            return Result.failure("缺少参数");
        }

        Optional<WxWorkContactWayBizAccountConfEntity> accountConf = contactWayBizAccountConfRepository.findById(id);
        if (!accountConf.isPresent()) {
            return Result.failure("not find user");
        }
        WxWorkContactWayBizAccountConfEntity account = accountConf.get();

        // 删除
        contactWayBizAccountConfRepository.deleteById(account.getId());

        return Result.success("success");
    }

    private List<String> getOaNames() {
        String oaNamesKey = "DJJX_AUNT_QYWX_CONFIG";
        RBucket<String> bucket = redisson.getBucket(oaNamesKey, StringCodec.INSTANCE);
        String oaNames = bucket.get();
        if (ObjectUtils.isEmpty(oaNames)) {
            return Lists.newArrayList("chengtaiqi", "caojing09", "wangyanrui", "wangyan01-wb");
        }
        return Arrays.stream(oaNames.split(",")).collect(Collectors.toList());
    }

    @org.jetbrains.annotations.NotNull
    private Map<String, String> getCityId2NameMap() {
        Result<List<CityListResp>> cityListRes = this.listCity("");
        // 新增全国
        CityListResp cityListResp = new CityListResp();
        cityListResp.setCityId("0");
        cityListResp.setCityName("全国");
        cityListRes.getData().add(cityListResp);
        return cityListRes.getData().stream()
                .collect(Collectors.toMap(CityListResp::getCityId, CityListResp::getCityName));
    }

    @Override
    @VerifiedParams
    @GlobalExceptionWrapper
    public Result<String> updateUserAiState(Long id, Integer state) {
        if (ObjectUtils.isEmpty(id) || ObjectUtils.isEmpty(state) || !Arrays.asList(0, 1).contains(state)) {
            return Result.failure("参数错误");
        }

        Optional<WxWorkContactWayBizAccountConfEntity> accountConf = contactWayBizAccountConfRepository.findById(id);
        if (!accountConf.isPresent()) {
            return Result.failure("not find user");
        }

        WxWorkContactWayBizAccountConfEntity account = accountConf.get();
        String stateDesc = Objects.equals(state, 0) ? "下线AI" : "上线AI";
        Date curDate = new Date();

        try {
            if (Objects.equals(state, 0)) {
                // 下线AI
                aiInterventionConditionsComponent.closeAiByUserId(account.getCorpId(),
                        account.getUserId());
            } else if (Objects.equals(state, 1)) {
                // 上线AI
                aiInterventionConditionsComponent.openAiByUserId(account.getCorpId(),
                        account.getUserId());
            }

            // 记录操作日志
            AccountConfOperatorLogEntity operatorLog = new AccountConfOperatorLogEntity();
            operatorLog.setOperator("后台");
            operatorLog.setAccountConfId(account.getId());
            operatorLog.setLog(DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss, curDate) + System.lineSeparator() +
                    "后台操作" + BizSceneEnum.strictOf(account.getBizLine(), account.getBizScene()).getSceneName() + System.lineSeparator() +
                    "账户：" + account.getUserId() + stateDesc);
            operatorLog.setCreateTime(curDate);
            operatorLog.setUpdateTime(curDate);
            accountConfOperatorLogRepository.save(operatorLog);

            return Result.success("success");

        } catch (Exception e) {
            return Result.failure("操作失败");
        }
    }


    @Override
    @VerifiedParams
    @GlobalExceptionWrapper
    public Result<ContactWayForHyResp> getContactWayForHy(@NotNull @Valid final ContactWayForHyReq req) {
        log.info("使用绑定关系策略getContactWayForHy，请求参数：{}", JSON.toJSONString(req));
        if (!Objects.equals(req.getBizLine(), BizLineEnum.主站.getCode())) {
            return Result.failure("只有主站可以使用绑定关系策略");
        }

        UserContactWayResult result = bindRelationContactWayAccountStrategy.getUserIds(req);
        if (result == null || ObjectUtils.isEmpty(result.getUserIds())) {
            return Result.failure("暂无可用账号，请联系相应负责人");
        }

        String userId = result.getUserIds().get(0);
        ContactWayForHyResp contactWayResp = new ContactWayForHyResp();
        contactWayResp.setHasFriendRelation(result.getHasFriendRelation());

        // 根据好友关系状态决定返回内容
        if (result.getHasFriendRelation()) {
            // 已加好友：返回userId，不返回linkUrl
            contactWayResp.setUserId(userId);
            log.info("使用绑定关系策略，已有好友关系，返回客服userId：{}", userId);
        } else {
            // 未加好友：不返回userId，返回linkUrl
            // 从Redis获取userId对应的linkUrl，使用bizLine和bizScene区分
            String redisKey = String.format("USER_LINK_URL_MAP_%s_%s", req.getBizLine(), req.getBizScene());
            RBucket<String> userLink = redisson.getBucket(redisKey, StringCodec.INSTANCE);
            if (ObjectUtils.isEmpty(userLink.get())) {
                return Result.failure("暂无可用链接，请联系相应负责人");
            }
            contactWayResp.setLinkUrl(userLink.get());
            log.info("使用绑定关系策略，无好友关系，返回linkUrl：{}", contactWayResp.getLinkUrl());
        }

        return Result.success(contactWayResp);
    }

}
