package com.bj58.hy.wx.qywxbiz.interfaces.wmb.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.configuration.wmb.WmbProperties;
import com.bj58.hy.wx.qywxbiz.infrastructure.util.wmb_framework.AbstractWmbHandler;
import com.bj58.hy.wx.qywxbiz.service.bo.BizSceneEnum;
import com.bj58.hy.wx.qywxbiz.service.common.group_chat_result_event_handler.AbstractGroupChatResultEventHandler;
import com.bj58.spat.esbclient.ESBClient;
import com.bj58.spat.esbclient.ESBMessage;
import com.bj58.spat.esbclient.ESBReceiveHandler;
import com.bj58.spat.esbclient.ESBSubject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

@Slf4j
@Component
public class GroupChatResultCallback extends AbstractWmbHandler {

    private ESBClient client;

    private WmbProperties.Client.Publish publish;

    @Autowired
    private ObjectProvider<AbstractGroupChatResultEventHandler> eventHandlers;


    @Override
    protected int getWmbSubjectId() {
        return publish.getSubjectId();
    }

    @Override
    protected ESBClient getWmbClient() {
        return this.client;
    }

    @Override
    protected String getWarningPrefix() {
        return "[group_chat_result]";
    }

    @Override
    protected void init() {
        publish = wmbProperties.getPublish("group_chat_result");

        WmbProperties.Client.Subscribe subscribe = wmbProperties.getSubscribe("group_chat_result");

        if (Objects.isNull(subscribe)) {
            log.info("No configuration item exists, " + getWarningPrefix() + " wmb client init failure. ");
            return;
        }

        if (!subscribe.isEnabled()) {
            log.info(getWarningPrefix() + " esb subscribe function has not been activated");
            return;
        }

        try {
            client = new ESBClient(wmbProperties.getPath());
            client.setReceiveSubject(new ESBSubject(
                    subscribe.getSubjectId(), subscribe.getClientId()
            ));
            client.setReceiveHandler(new ESBReceiveHandler() {
                @Override
                public void messageReceived(final ESBMessage esbMessage) {
                    handler(esbMessage);
                }
            });

            log.info(getWarningPrefix() + " esb client init success");
        } catch (Exception e) {
            log.error(String.format(getWarningPrefix() + " esb client init failed: %s", e.getMessage()), e);
        }
    }


    public void handler(ESBMessage esbMessage) {
        String esbMsg = new String(esbMessage.getBody(), StandardCharsets.UTF_8);
        handler(esbMsg);
    }


    public void handler(String esbMsg) {
        log.info("receive group chat result callback, body = {}", esbMsg);
        if (ObjectUtils.isEmpty(esbMsg)) {
            return;
        }

        JSONObject event = JSONObject.parseObject(esbMsg);
        if (ObjectUtils.isEmpty(event)) {
            return;
        }

        for (AbstractGroupChatResultEventHandler eventHandler : eventHandlers) {
            try {
                if (!eventHandler.matched(event)) {
                    continue;
                }

                eventHandler.process(event);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }
}
