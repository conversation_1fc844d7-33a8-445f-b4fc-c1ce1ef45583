package com.bj58.hy.wx.qywxbiz.infrastructure.configuration;

import com.bj58.hy.wx.qywxbiz.service.common.monitor.MonitorAlarmConfig;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 监控系统配置
 * 
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties({MonitorAlarmConfig.class})
public class MonitorConfiguration {
    // 配置类，用于启用监控相关的配置属性
}
