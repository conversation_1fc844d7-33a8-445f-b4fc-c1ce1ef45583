package com.bj58.hy.wx.qywxbiz.service.common.contact_way;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.GetExternalContactRelationshipByBindOrBiz58IdReq;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.GetExternalContactRelationshipResp;
import com.bj58.hy.wx.qywxbiz.contract.dto.contactway.ContactWayForHyReq;
import com.bj58.hy.wx.qywxbiz.contract.dto.contactway.ContactWayReq;
import com.bj58.hy.wx.qywxbiz.entity.WxWorkContactWayBizAccountConfEntity;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ExternalContactRemoteService;
import com.bj58.hy.wx.qywxbiz.repository.WxWorkContactWayBizAccountConfRepository;
import com.bj58.hy.wx.qywxbiz.service.common.contact_way.bo.UserContactWayResult;
import com.bj58.hy.wx.qywxbiz.service.common.contact_way.strategy.PollingGetContactWayBizAccountStrategy;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 根据bindWuBa和bindBiz查询已添加的客服好友关系策略
 */
@Service
@Slf4j
public class BindRelationContactWayAccountStrategy {

    @Autowired
    private ExternalContactRemoteService externalContactRemoteService;

    @Autowired
    protected WxWorkContactWayBizAccountConfRepository contactWayBizAccountConfRepository;

    @Autowired
    protected PollingGetContactWayBizAccountStrategy pollingGetContactWayBizAccountStrategy;

    @Nullable
    public UserContactWayResult getUserIds(@NonNull ContactWayForHyReq contactWayReq) {
        try {
            // 获取数据库配置的可用客服账号
            List<WxWorkContactWayBizAccountConfEntity> accountEntities =
                    contactWayBizAccountConfRepository.getAvailableUser(
                            contactWayReq.getBizLine(), Collections.singleton(contactWayReq.getBizScene()),
                            contactWayReq.getCorpId(), contactWayReq.getCityId());

            if (ObjectUtils.isEmpty(accountEntities)) {
                log.warn("未找到可用的客服账号配置，bizLine={}, bizScene={}, corpId={}, cityId={}",
                        contactWayReq.getBizLine(), contactWayReq.getBizScene(),
                        contactWayReq.getCorpId(), contactWayReq.getCityId());
                return null;
            }

            List<String> availableUserIds = accountEntities.stream()
                    .map(WxWorkContactWayBizAccountConfEntity::getUserId)
                    .collect(Collectors.toList());

            String existingUserId = null;

            // 优先查询bindWuBa对应的客服好友关系
            if (Objects.nonNull(contactWayReq.getBindWuBa())) {
                existingUserId = getWuBaLinkedUserId(contactWayReq.getCorpId(),
                        contactWayReq.getBindWuBa(), availableUserIds);
                log.info("查询58用户{}的已添加客服关系，找到客服：{}",
                        contactWayReq.getBindWuBa(), existingUserId);
            }

            // 如果通过bindWuBa没找到，再查询bindBiz对应的客服好友关系
            if (StringUtils.isEmpty(existingUserId) && StringUtils.isNotEmpty(contactWayReq.getBindBiz())) {
                existingUserId = getBizKeyLinkedUserId(contactWayReq.getCorpId(),
                        contactWayReq.getBindBiz(), availableUserIds);
                log.info("查询业务key{}的已添加客服关系，找到客服：{}",
                        contactWayReq.getBindBiz(), existingUserId);
            }

            if (StringUtils.isNotEmpty(existingUserId)) {
                // 找到了已添加的客服好友，直接返回该客服userId
                log.info("找到已添加的客服好友，客服userId：{}", existingUserId);
                return new UserContactWayResult(Collections.singletonList(existingUserId), true);
            } else {
                // 没有找到已添加的客服好友，使用轮询获得客服userId
                log.info("未找到已添加的客服好友，使用轮询策略获取客服");
                ContactWayReq req = new ContactWayReq();
                req.setCorpId(contactWayReq.getCorpId());
                req.setBizLine(contactWayReq.getBizLine());
                req.setBizScene(contactWayReq.getBizScene());
                req.setCityId(contactWayReq.getCityId());

                List<String> pollingUserIds = pollingGetContactWayBizAccountStrategy.getUser(req);
                return new UserContactWayResult(pollingUserIds, false);
            }

        } catch (Exception e) {
            log.error("BindRelationContactWayAccountStrategy处理失败，错误信息：{}", e.getMessage(), e);
            return null;
        }
    }



    /**
     * 根据58用户ID查询已添加的客服userId
     */
    private String getWuBaLinkedUserId(@NonNull final String corpId,
                                       @NonNull final Long wubaId,
                                       @NonNull final List<String> availableUserIds) {
        try {
            GetExternalContactRelationshipByBindOrBiz58IdReq getRelationshipReq =
                    new GetExternalContactRelationshipByBindOrBiz58IdReq();
            getRelationshipReq.setCorpId(corpId);
            getRelationshipReq.setWubaUid(wubaId);
            getRelationshipReq.setUserIds(availableUserIds);

            Result<List<GetExternalContactRelationshipResp>> result =
                    externalContactRemoteService.getExternalContactRelationshipByBindOrBiz58Id(getRelationshipReq);

            if (result != null && result.isSuccess() && ObjectUtils.notEmpty(result.getData())) {
                // 返回第一个有效的客服关系
                return result.getData().stream()
                        .filter(relationship -> Objects.equals(relationship.getStatus(), 1)) // 状态为1表示有效
                        .map(GetExternalContactRelationshipResp::getUserId)
                        .findFirst()
                        .orElse(null);
            }

        } catch (Exception e) {
            log.error("查询58用户{}的客服好友关系失败，错误信息：{}", wubaId, e.getMessage(), e);
        }

        return null;
    }

    /**
     * 根据业务key查询已添加的客服userId
     */
    private String getBizKeyLinkedUserId(@NonNull final String corpId,
                                         @NonNull final String bizKey,
                                         @NonNull final List<String> availableUserIds) {
        try {
            log.info("开始查询业务key{}对应的客服好友关系，可用客服：{}", bizKey, availableUserIds);

            // 使用IExternalContactMappingService.getBizKeyLinkedExternalUsers查询绑定了该bizKey的外部用户
            List<String> linkedExternalUsers = externalContactRemoteService.getBizKeyLinkedExternalUsers(corpId, bizKey);

            if (ObjectUtils.notEmpty(linkedExternalUsers)) {
                log.info("找到业务key{}绑定的外部用户：{}", bizKey, linkedExternalUsers);

                // 默认第一个外部用户，查询其与可用客服的关系
                String externalUserId = linkedExternalUsers.get(0);

                for (String userId : availableUserIds) {
                    // 查询该外部用户与客服的关系
                    try {
                        // 使用现有的getRelationship方法
                        GetExternalContactRelationshipResp relationship = externalContactRemoteService.getRelationship(corpId, userId, externalUserId);

                        if (relationship != null && Objects.equals(relationship.getStatus(), 1)) {
                            log.info("找到业务key{}对应的有效客服关系，客服：{}，外部用户：{}", bizKey, userId, externalUserId);
                            return userId;
                        }
                    } catch (Exception e) {
                        log.error("查询外部用户{}与客服{}的关系失败：{}", externalUserId, userId, e.getMessage());
                    }
                }
            }

            log.info("未找到业务key{}对应的有效客服好友关系", bizKey);

        } catch (Exception e) {
            log.error("查询业务key{}的客服好友关系失败，错误信息：{}", bizKey, e.getMessage(), e);
        }

        return null;
    }
} 