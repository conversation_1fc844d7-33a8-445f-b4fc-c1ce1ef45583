package com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler.impl.wx_work_state;

import cn.hutool.core.thread.BlockPolicy;
import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.IMessageService;
import com.bj58.hy.wx.qywx.contract.dto.message.MessageResp;
import com.bj58.hy.wx.qywx.contract.dto.message.SendMessageReq;
import com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler.AbstractWxWorkEventHandler;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.NonNull;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.IntegerCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Description:
 *
 * <AUTHOR>
 */
@Component
public class ProcessPassThroughBizMessageWxWorkEventHandler extends AbstractWxWorkEventHandler {

    @Autowired
    protected RedissonClient redisson;

    @SCFClient(lookup = IMessageService.SCF_URL)
    private IMessageService messageService;

    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(
            5, 10,
            1, TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("SendPassThroughBizMessage-%d").build(),
            new BlockPolicy(Runnable::run)
    );

    @Override
    public void process(final @NonNull JSONObject event) {
        try {
            final String corpId = event.getString("ToUserName");
            final String userId = event.getString("UserID");
            final String externalUserId = event.getString("ExternalUserID");
            if (ObjectUtils.isEmpty(corpId) || ObjectUtils.isEmpty(userId) || ObjectUtils.isEmpty(externalUserId)) {
                return;
            }

            JSONObject ext = event.getJSONObject("Ext");
            if (ObjectUtils.isEmpty(ext)) {
                return;
            }

            JSONObject stateMappedVal = ext.getJSONObject("StateMappedVal");
            if (ObjectUtils.isEmpty(stateMappedVal)) {
                return;
            }

            JSONObject bizMessage = stateMappedVal.getJSONObject("bizMessage");
            if (ObjectUtils.isNull(bizMessage)) {
                return;
            }

            Integer messageType = bizMessage.getInteger("messageType");
            String payload = bizMessage.getString("payload");
            if (ObjectUtils.isNull(messageType) || ObjectUtils.isEmpty(payload)) {
                return;
            }

            executor.execute(() -> {
                log.info("found external user linked biz message, event info = {}", event.toJSONString());

                int maxRetries = getBizMessageSendMaxRetries();
                int retryInterval = getBizMessageSendRetryInterval();

                final SendMessageReq sendMessageReq = new SendMessageReq();
                sendMessageReq.setCorpId(corpId);
                sendMessageReq.setWecomUserId(userId);
                sendMessageReq.setExternalUserId(externalUserId);
                sendMessageReq.setMessageType(messageType);
                sendMessageReq.setPayload(payload);

                int i = 0;
                while (true) {
                    Result<MessageResp> result = null;
                    try {
                        result = messageService.send(sendMessageReq);

                        if (ObjectUtils.notNull(result) || !result.isSuccess()) {
                            throw new RuntimeException("send biz message fizzle out");
                        }

                        log.info("send biz message success, message info = {}, result = {}",
                                JacksonUtils.format(sendMessageReq), JacksonUtils.format(result));
                        break;

                    } catch (Exception e) {
                        ++i;

                        log.error("send biz message error, message info = {}, curr result = {}, curr number of errors = {}, maximum number of retry = {}, error msg = {}",
                                JacksonUtils.format(sendMessageReq), JacksonUtils.format(result), i, maxRetries, e.getMessage());

                        if (i > maxRetries) { // 超过了最大重试次数
                            break;
                        }

                        if (retryInterval > 0) { // 未达最大重试次数，尝试休眠后重试
                            try {
                                TimeUnit.SECONDS.sleep(retryInterval);
                            } catch (InterruptedException ignored) {
                            }
                        }
                    }
                }
            });


        } catch (Exception e) {
            log.error("ProcessPassThroughBizMessageWxWorkEventHandler process error , event:{}", JSONObject.toJSONString(event), e);
        }
    }

    @Override
    public boolean matched(final @NonNull JSONObject event) {
        return true;
    }


    private int getBizMessageSendMaxRetries() {
        int maxRetries = 10;
        RBucket<Integer> bucket = redisson.getBucket("BizMessage_MAX_RETRIES", IntegerCodec.INSTANCE);
        if (bucket.isExists()) {
            maxRetries = bucket.get();
        }

        if (maxRetries < 0) {
            maxRetries = 10;
        }

        if (maxRetries > 30) {
            maxRetries = 10;
        }

        return maxRetries;
    }

    private int getBizMessageSendRetryInterval() {
        int interval = 0;

        RBucket<Integer> bucket = redisson.getBucket("BizMessage_RETRY_INTERVAL", IntegerCodec.INSTANCE);
        if (bucket.isExists()) {
            interval = bucket.get();
        }

        if (interval < 0) {
            interval = 0;
        }

        return interval;
    }

}
