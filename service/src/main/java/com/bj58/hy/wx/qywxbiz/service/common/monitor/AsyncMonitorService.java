package com.bj58.hy.wx.qywxbiz.service.common.monitor;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * 异步监控服务
 * 将监控数据存储到Redis中，支持3天数据保留
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AsyncMonitorService {

    @Autowired
    private RedissonClient redisson;

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final int EXPIRE_DAYS = 3; // 数据保留3天

    /**
     * 记录监控数据到Redis
     */
    private void recordToRedis(String key, Object value) {
        try {
            String timestamp = LocalDateTime.now().format(FORMATTER);
            String data = String.format("%s|%s", timestamp, value.toString());

            RList<String> list = redisson.getList(key, StringCodec.INSTANCE);
            list.add(data);
            list.expire(EXPIRE_DAYS, TimeUnit.DAYS);

            // 限制列表大小，保留最近1000条记录
            if (list.size() > 1000) {
                // 删除最旧的记录，保留最新的1000条
                int toRemove = list.size() - 1000;
                for (int i = 0; i < toRemove; i++) {
                    list.remove(0);
                }
            }
        } catch (Exception e) {
            log.error("记录监控数据到Redis失败: key={}, value={}", key, value, e);
        }
    }

    /**
     * 增加计数器
     */
    private void incrementCounter(String key) {
        try {
            String dateKey = key + ":" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // 使用RAtomicLong来实现原子性的计数器操作
            RAtomicLong atomicLong = redisson.getAtomicLong(dateKey);
            atomicLong.incrementAndGet();
            atomicLong.expire(EXPIRE_DAYS, TimeUnit.DAYS);

        } catch (Exception e) {
            log.error("增加计数器失败: key={}", key, e);
        }
    }

    // ==================== 欢迎语监控异步方法 ====================

    @Async("monitorExecutor")
    public void recordWelcomeGenerateSuccess(String corpId, String bizScene) {
        try {
            incrementCounter("welcome:generate:success:" + corpId + ":" + bizScene);
            recordToRedis("welcome:generate:success:detail", corpId + "|" + bizScene);
        } catch (Exception e) {
            log.error("异步记录欢迎语生成成功失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordWelcomeGenerateFailure(String corpId, String bizScene, String reason) {
        try {
            incrementCounter("welcome:generate:failure:" + corpId + ":" + bizScene);
            recordToRedis("welcome:generate:failure:detail", corpId + "|" + bizScene + "|" + reason);
        } catch (Exception e) {
            log.error("异步记录欢迎语生成失败失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordWelcomeSendSuccess(String corpId, String userId) {
        try {
            incrementCounter("welcome:send:success:" + corpId + ":" + userId);
            recordToRedis("welcome:send:success:detail", corpId + "|" + userId);
        } catch (Exception e) {
            log.error("异步记录欢迎语发送成功失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordWelcomeSendFailure(String corpId, String userId, String reason) {
        try {
            incrementCounter("welcome:send:failure:" + corpId + ":" + userId);
            recordToRedis("welcome:send:failure:detail", corpId + "|" + userId + "|" + reason);
        } catch (Exception e) {
            log.error("异步记录欢迎语发送失败失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordWelcomeRetryCount(String corpId, int retryCount) {
        try {
            recordToRedis("welcome:retry:count", corpId + "|" + retryCount);
        } catch (Exception e) {
            log.error("异步记录欢迎语重试次数失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordWelcomeSendDuration(String corpId, int retryCount, long duration) {
        try {
            recordToRedis("welcome:send:duration", corpId + "|" + retryCount + "|" + duration);
        } catch (Exception e) {
            log.error("异步记录欢迎语发送耗时失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordWelcomeCacheHit() {
        try {
            incrementCounter("welcome:cache:hit");
        } catch (Exception e) {
            log.error("异步记录欢迎语缓存命中失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordWelcomeCacheMiss() {
        try {
            incrementCounter("welcome:cache:miss");
        } catch (Exception e) {
            log.error("异步记录欢迎语缓存未命中失败", e);
        }
    }

    // ==================== AI聊天监控异步方法 ====================

    @Async("monitorExecutor")
    public void recordAiCallSuccess(String corpId, String callType) {
        try {
            incrementCounter("ai:call:success:" + corpId + ":" + callType);
            recordToRedis("ai:call:success:detail", corpId + "|" + callType);
        } catch (Exception e) {
            log.error("异步记录AI调用成功失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordAiCallFailure(String corpId, String callType, String reason) {
        try {
            incrementCounter("ai:call:failure:" + corpId + ":" + callType);
            recordToRedis("ai:call:failure:detail", corpId + "|" + callType + "|" + reason);
        } catch (Exception e) {
            log.error("异步记录AI调用失败失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordAiResponseTime(String corpId, String callType, long responseTime) {
        try {
            recordToRedis("ai:response:time", corpId + "|" + callType + "|" + responseTime);
        } catch (Exception e) {
            log.error("异步记录AI响应时间失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordAiResultType(String corpId, Integer resultType) {
        try {
            incrementCounter("ai:result:type:" + corpId + ":" + resultType);
            recordToRedis("ai:result:type:detail", corpId + "|" + resultType);
        } catch (Exception e) {
            log.error("异步记录AI结果类型失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordAiTakeoverSuccess(String corpId, String userId) {
        try {
            incrementCounter("ai:takeover:success:" + corpId + ":" + userId);
            recordToRedis("ai:takeover:success:detail", corpId + "|" + userId);
        } catch (Exception e) {
            log.error("异步记录AI承接成功失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordAiTakeoverFailure(String corpId, String userId, String reason) {
        try {
            incrementCounter("ai:takeover:failure:" + corpId + ":" + userId);
            recordToRedis("ai:takeover:failure:detail", corpId + "|" + userId + "|" + reason);
        } catch (Exception e) {
            log.error("异步记录AI承接失败失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordAsyncTaskQueueSize(int queueSize) {
        try {
            recordToRedis("ai:async:queue:size", String.valueOf(queueSize));
        } catch (Exception e) {
            log.error("异步记录异步任务队列大小失败", e);
        }
    }

    // ==================== 消息处理监控异步方法 ====================

    @Async("monitorExecutor")
    public void recordChatMessageTotal(String corpId, String messageType) {
        try {
            incrementCounter("message:total:" + corpId + ":" + messageType);
            recordToRedis("message:total:detail", corpId + "|" + messageType);
        } catch (Exception e) {
            log.error("异步记录消息处理总量失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordMessageProcessLatency(String corpId, String stage, long latency) {
        try {
            recordToRedis("message:latency", corpId + "|" + stage + "|" + latency);
        } catch (Exception e) {
            log.error("异步记录消息处理延迟失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordFirstConsultTakeoverSuccess(String corpId) {
        try {
            incrementCounter("first:consult:success:" + corpId);
            recordToRedis("first:consult:success:detail", corpId);
        } catch (Exception e) {
            log.error("异步记录首次咨询承接成功失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordFirstConsultTakeoverFailure(String corpId, String reason) {
        try {
            incrementCounter("first:consult:failure:" + corpId);
            recordToRedis("first:consult:failure:detail", corpId + "|" + reason);
        } catch (Exception e) {
            log.error("异步记录首次咨询承接失败失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordHumanIntervention(String corpId, String userId, String reason) {
        try {
            incrementCounter("human:intervention:" + corpId + ":" + userId);
            recordToRedis("human:intervention:detail", corpId + "|" + userId + "|" + reason);
        } catch (Exception e) {
            log.error("异步记录人工介入失败", e);
        }
    }

    // ==================== 用户满意度监控异步方法 ====================

    @Async("monitorExecutor")
    public void recordSatisfactionSurveySent(String corpId, String userId) {
        try {
            incrementCounter("satisfaction:survey:sent:" + corpId + ":" + userId);
            recordToRedis("satisfaction:survey:sent:detail", corpId + "|" + userId);
        } catch (Exception e) {
            log.error("异步记录满意度调查发送失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordUserSatisfactionPositive(String corpId, String userId, String satisfactionLevel) {
        try {
            incrementCounter("satisfaction:positive:" + corpId + ":" + userId);
            recordToRedis("satisfaction:positive:detail", corpId + "|" + userId + "|" + satisfactionLevel);
        } catch (Exception e) {
            log.error("异步记录用户满意反馈失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordUserSatisfactionNegative(String corpId, String userId, String reason) {
        try {
            incrementCounter("satisfaction:negative:" + corpId + ":" + userId);
            recordToRedis("satisfaction:negative:detail", corpId + "|" + userId + "|" + reason);
        } catch (Exception e) {
            log.error("异步记录用户不满意反馈失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordFeedbackRecoverySent(String corpId, String userId, String externalUserId) {
        try {
            incrementCounter("feedback:recovery:sent:" + corpId + ":" + userId);
            recordToRedis("feedback:recovery:sent:detail", corpId + "|" + userId + "|" + externalUserId);
        } catch (Exception e) {
            log.error("异步记录反馈回收发送失败", e);
        }
    }

    // ==================== 外部服务监控异步方法 ====================

    @Async("monitorExecutor")
    public void recordDifyApiCallSuccess(String corpId) {
        try {
            incrementCounter("dify:api:success:" + corpId);
            recordToRedis("dify:api:success:detail", corpId);
        } catch (Exception e) {
            log.error("异步记录Dify API调用成功失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordDifyApiCallFailure(String corpId, String reason) {
        try {
            incrementCounter("dify:api:failure:" + corpId);
            recordToRedis("dify:api:failure:detail", corpId + "|" + reason);
        } catch (Exception e) {
            log.error("异步记录Dify API调用失败失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordOrderQuerySuccess(String orderType) {
        try {
            incrementCounter("order:query:success:" + orderType);
            recordToRedis("order:query:success:detail", orderType);
        } catch (Exception e) {
            log.error("异步记录订单查询成功失败", e);
        }
    }

    @Async("monitorExecutor")
    public void recordOrderQueryFailure(String orderType, String reason) {
        try {
            incrementCounter("order:query:failure:" + orderType);
            recordToRedis("order:query:failure:detail", orderType + "|" + reason);
        } catch (Exception e) {
            log.error("异步记录订单查询失败失败", e);
        }
    }
}
