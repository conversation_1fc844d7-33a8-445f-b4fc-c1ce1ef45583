package com.bj58.hy.wx.qywxbiz.entity;

import com.bj58.hy.lib.spring.support.jpa.superclass.Identifiable;
import lombok.*;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/11/20 11:42
 */
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "t_wx_work_contact_way_biz_account_conf", indexes = {
        @Index(name = "idx_biz", columnList = "biz_line, biz_scene, corp_id, user_id", unique = true),
})
public class WxWorkContactWayBizAccountConfEntity extends Identifiable {

    /**
     * 业务线
     */
    @Getter
    @Setter
    @Column(name = "biz_line", nullable = false)
    private Integer bizLine;

    /**
     * 业务场景
     */
    @Getter
    @Setter
    @Column(name = "biz_scene", nullable = false)
    private Integer bizScene;

    /**
     * 企业微信id
     */
    @Getter
    @Setter
    @Column(name = "corp_id", nullable = false, length = 18)
    private String corpId;

    /**
     * 企业成员id
     */
    @Getter
    @Setter
    @Column(name = "user_id", nullable = false, length = 64)
    private String userId;

    /**
     * 添加好友的速率，-1代表不限制
     */
    @Getter
    @Setter
    @Column(name = "rate", nullable = false)
    private Integer rate;

    /**
     * 速率间隔
     */
    @Getter
    @Setter
    @Column(name = "rate_interval", nullable = false)
    private Integer rateInterval;

    /**
     * 速率间隔单位：1.秒 2.分钟 3.小时 4.天
     */
    @Getter
    @Setter
    @Column(name = "rate_interval_unit", nullable = false)
    private Integer rateIntervalUnit;

    /**
     * 创建时间
     */
    @Getter
    @Setter
    @Column(name = "create_time", nullable = false)
    private Date createTime;

    /**
     * 更新时间
     */
    @Getter
    @Setter
    @Column(name = "update_time", nullable = false)
    private Date updateTime;

    /**
     * 状态
     */
    @Getter
    @Setter
    @Column(name = "state")
    private Integer state;

    /**
     * 城市id
     */
    @Getter
    @Setter
    @Column(name = "city_id", nullable = false)
    private String cityId;

}
