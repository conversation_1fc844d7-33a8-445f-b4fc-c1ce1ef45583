package com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan;

import com.bj58.hy.fx.bcore.contract.order.IOrderQueryService;
import com.bj58.hy.fx.bcore.entity.FxCoreResponseT;
import com.bj58.hy.fx.bcore.entity.neworder.OrderEntity;
import com.bj58.hy.fx.bcore.entity.neworder.query.OrderQueryEntity;
import com.bj58.hy.fx.bcore.entity.order.COrderListEntity;
import com.bj58.hy.fx.bcore.entity.order.COrderWashInfoEntity;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.bo.OrderQueryReq;
import com.bj58.hy.wx.qywxbiz.service.common.monitor.AsyncMonitorService;
import com.bj58.spat.scf.spring.boot.annotation.SCFClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/13 10:50
 */
@Slf4j
@Component
public class JingXuanOrderQueryService {

    @SCFClient(lookup = IOrderQueryService.SCF_URL)
    private IOrderQueryService orderQueryService;

    @Autowired
    private AsyncMonitorService asyncMonitorService;

    /**
     * 根据订单id获取精选信息
     */
    public OrderEntity query(Long orderId) {

        try {
            FxCoreResponseT<OrderEntity> orderInfoResult = orderQueryService.getOrderInfoByOrderId(orderId);
            if (orderInfoResult != null && orderInfoResult.getData() != null) {
                // 异步记录订单查询成功
                asyncMonitorService.recordOrderQuerySuccess("jingxuan");
                return orderInfoResult.getData();
            } else {
                // 异步记录订单查询失败
                asyncMonitorService.recordOrderQueryFailure("jingxuan", "查询结果为空");
            }
        } catch (Exception e) {
            log.error("BcoreOrderQueryService query error", e);
            // 异步记录订单查询失败
            asyncMonitorService.recordOrderQueryFailure("jingxuan", e.getMessage());
        }

        return null;
    }


    public COrderWashInfoEntity getWashInfo(Long orderId,Long userId){

        try {
            FxCoreResponseT<COrderWashInfoEntity> washInfoForDify = orderQueryService.getWashInfoForDify(orderId, userId);

            if (washInfoForDify != null &&
                    washInfoForDify.isSuccess() &&
                    washInfoForDify.getData() != null) {
                return washInfoForDify.getData();
            }
        } catch (Exception e) {
            log.error("BcoreOrderQueryService getWashInfo error", e);
        }

        return null;
    }


    public List<COrderListEntity> geUserOrderList(OrderQueryReq orderQueryReq){

        try {
            OrderQueryEntity orderQueryEntity = new OrderQueryEntity();
            orderQueryEntity.setWubaId(orderQueryReq.getWbUserId());


            if (ObjectUtils.notEmpty(orderQueryReq.getCateId())){
                orderQueryEntity.setCateId(orderQueryReq.getCateId());
            }

            if (ObjectUtils.notEmpty(orderQueryReq.getCreateStartTime())){
                orderQueryEntity.setCreateStartTime(orderQueryReq.getCreateStartTime());
            }

            if (ObjectUtils.notEmpty(orderQueryReq.getCreateEndTime())){
                orderQueryEntity.setCreateEndTime(orderQueryReq.getCreateEndTime());
            }
            orderQueryEntity.setPageNo(orderQueryReq.getPage());
            orderQueryEntity.setPageSize(orderQueryReq.getSize());

            FxCoreResponseT<List<COrderListEntity>> cOrderListResult = orderQueryService.getCOrderList(orderQueryEntity);
            log.info("getCOrderList req:{},result:{}",JacksonUtils.format(orderQueryEntity),JacksonUtils.format(cOrderListResult));
            if (cOrderListResult.isSuccess()){
                return cOrderListResult.getData();
            }

        } catch (Exception e) {
            log.error("JingXuanOrderQueryService geUserOrderList error  req:" + JacksonUtils.format(orderQueryReq),e);
        }

        return null;
    }
}
