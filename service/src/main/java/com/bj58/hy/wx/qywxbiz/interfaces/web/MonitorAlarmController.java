package com.bj58.hy.wx.qywxbiz.interfaces.web;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.wx.qywxbiz.service.common.monitor.MonitorAlarmConfig;
import com.bj58.hy.wx.qywxbiz.service.common.monitor.MonitorAlarmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 监控告警控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/monitor/alarm")
public class MonitorAlarmController {

    @Autowired
    private MonitorAlarmService monitorAlarmService;

    @Autowired
    private MonitorAlarmConfig alarmConfig;

    /**
     * 手动触发所有监控告警检查
     */
    @PostMapping("/check-all")
    public Result<String> checkAllAlarms(@RequestParam(defaultValue = "ww5") String corpId) {
        try {
            monitorAlarmService.checkAllAlarms(corpId);
            return Result.success("监控告警检查完成");
        } catch (Exception e) {
            log.error("手动触发监控告警检查失败: corpId={}", corpId, e);
            return Result.failure("监控告警检查失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发欢迎语监控告警检查
     */
    @PostMapping("/check-welcome")
    public Result<String> checkWelcomeAlarms(
            @RequestParam(defaultValue = "ww5") String corpId,
            @RequestParam(defaultValue = "default") String bizScene) {
        try {
            monitorAlarmService.checkWelcomeMessageAlarms(corpId, bizScene);
            return Result.success("欢迎语监控告警检查完成");
        } catch (Exception e) {
            log.error("手动触发欢迎语监控告警检查失败: corpId={}, bizScene={}", corpId, bizScene, e);
            return Result.failure("欢迎语监控告警检查失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发AI聊天监控告警检查
     */
    @PostMapping("/check-ai-chat")
    public Result<String> checkAiChatAlarms(@RequestParam(defaultValue = "ww5") String corpId) {
        try {
            monitorAlarmService.checkAiChatAlarms(corpId);
            return Result.success("AI聊天监控告警检查完成");
        } catch (Exception e) {
            log.error("手动触发AI聊天监控告警检查失败: corpId={}", corpId, e);
            return Result.failure("AI聊天监控告警检查失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发消息处理监控告警检查
     */
    @PostMapping("/check-message-process")
    public Result<String> checkMessageProcessAlarms(@RequestParam(defaultValue = "ww5") String corpId) {
        try {
            monitorAlarmService.checkMessageProcessAlarms(corpId);
            return Result.success("消息处理监控告警检查完成");
        } catch (Exception e) {
            log.error("手动触发消息处理监控告警检查失败: corpId={}", corpId, e);
            return Result.failure("消息处理监控告警检查失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发用户满意度监控告警检查
     */
    @PostMapping("/check-user-satisfaction")
    public Result<String> checkUserSatisfactionAlarms(@RequestParam(defaultValue = "ww5") String corpId) {
        try {
            monitorAlarmService.checkUserSatisfactionAlarms(corpId);
            return Result.success("用户满意度监控告警检查完成");
        } catch (Exception e) {
            log.error("手动触发用户满意度监控告警检查失败: corpId={}", corpId, e);
            return Result.failure("用户满意度监控告警检查失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发外部服务监控告警检查
     */
    @PostMapping("/check-external-service")
    public Result<String> checkExternalServiceAlarms(@RequestParam(defaultValue = "ww5") String corpId) {
        try {
            monitorAlarmService.checkExternalServiceAlarms(corpId);
            return Result.success("外部服务监控告警检查完成");
        } catch (Exception e) {
            log.error("手动触发外部服务监控告警检查失败: corpId={}", corpId, e);
            return Result.failure("外部服务监控告警检查失败: " + e.getMessage());
        }
    }

    /**
     * 批量检查多个企业的监控告警
     */
    @PostMapping("/check-batch")
    public Result<String> checkBatchAlarms(@RequestParam String[] corpIds) {
        try {
            monitorAlarmService.checkAllAlarmsForCorps(corpIds);
            return Result.success("批量监控告警检查完成，共检查 " + corpIds.length + " 个企业");
        } catch (Exception e) {
            log.error("批量触发监控告警检查失败: corpIds={}", (Object) corpIds, e);
            return Result.failure("批量监控告警检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前告警配置
     */
    @GetMapping("/config")
    public Result<MonitorAlarmConfig> getAlarmConfig() {
        try {
            return Result.success(alarmConfig);
        } catch (Exception e) {
            log.error("获取告警配置失败", e);
            return Result.failure("获取告警配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取告警阈值说明
     */
    @GetMapping("/thresholds")
    public Result<Map<String, Object>> getAlarmThresholds() {
        try {
            Map<String, Object> thresholds = new HashMap<>();
            
            // 欢迎语告警阈值
            Map<String, Object> welcome = new HashMap<>();
            welcome.put("generateSuccessRate", alarmConfig.getWelcome().getGenerateSuccessRateThreshold() + "%");
            welcome.put("cacheHitRate", alarmConfig.getWelcome().getCacheHitRateThreshold() + "%");
            welcome.put("avgRetryCount", alarmConfig.getWelcome().getAvgRetryCountThreshold() + "次");
            welcome.put("sendDurationP99", alarmConfig.getWelcome().getSendDurationP99Threshold() + "ms");
            thresholds.put("welcome", welcome);
            
            // AI聊天告警阈值
            Map<String, Object> aiChat = new HashMap<>();
            aiChat.put("callSuccessRate", alarmConfig.getAiChat().getCallSuccessRateThreshold() + "%");
            aiChat.put("takeoverSuccessRate", alarmConfig.getAiChat().getTakeoverSuccessRateThreshold() + "%");
            aiChat.put("transferToHumanRate", alarmConfig.getAiChat().getTransferToHumanRateThreshold() + "%");
            aiChat.put("responseTimeP99", alarmConfig.getAiChat().getResponseTimeP99Threshold() + "ms");
            aiChat.put("asyncTaskBacklog", alarmConfig.getAiChat().getAsyncTaskBacklogThreshold() + "个");
            thresholds.put("aiChat", aiChat);
            
            // 消息处理告警阈值
            Map<String, Object> messageProcess = new HashMap<>();
            messageProcess.put("firstConsultTakeoverRate", alarmConfig.getMessageProcess().getFirstConsultTakeoverRateThreshold() + "%");
            messageProcess.put("processLatencyP95", alarmConfig.getMessageProcess().getProcessLatencyP95Threshold() + "ms");
            messageProcess.put("humanInterventionRate", alarmConfig.getMessageProcess().getHumanInterventionRateThreshold() + "%");
            messageProcess.put("messageQueueBacklog", alarmConfig.getMessageProcess().getMessageQueueBacklogThreshold() + "个");
            thresholds.put("messageProcess", messageProcess);
            
            // 用户满意度告警阈值
            Map<String, Object> userSatisfaction = new HashMap<>();
            userSatisfaction.put("satisfactionRate", alarmConfig.getUserSatisfaction().getSatisfactionRateThreshold() + "%");
            userSatisfaction.put("surveyResponseRate", alarmConfig.getUserSatisfaction().getSurveyResponseRateThreshold() + "%");
            userSatisfaction.put("surveyMinSampleSize", alarmConfig.getUserSatisfaction().getSurveyMinSampleSize() + "个");
            thresholds.put("userSatisfaction", userSatisfaction);
            
            // 外部服务告警阈值
            Map<String, Object> externalService = new HashMap<>();
            externalService.put("difyApiSuccessRate", alarmConfig.getExternalService().getDifyApiSuccessRateThreshold() + "%");
            externalService.put("orderQuerySuccessRate", alarmConfig.getExternalService().getOrderQuerySuccessRateThreshold() + "%");
            thresholds.put("externalService", externalService);
            
            return Result.success(thresholds);
        } catch (Exception e) {
            log.error("获取告警阈值说明失败", e);
            return Result.failure("获取告警阈值说明失败: " + e.getMessage());
        }
    }
}
