package com.bj58.hy.wx.qywxbiz.repository.bo;

import com.bj58.hy.lib.core.support.pojo.PagedQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Set;

/**
 * Description: 
 *
 * <AUTHOR>
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class PagedQueryWxWorkContactWayBizAccountConf extends PagedQuery {

    @NotEmpty
    private String corpId;

    @NotNull
    private Integer bizLine;

    @NotEmpty
    private Set<Integer> bizScenes;

    private Integer cityId;

    private Set<String> userIds;

    private Integer state;

    private Date createStartTime;
    private Date createEndTime;

    private Date updateStartTime;
    private Date updateEndTime;
}
