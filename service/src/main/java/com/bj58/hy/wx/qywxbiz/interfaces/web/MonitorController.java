package com.bj58.hy.wx.qywxbiz.interfaces.web;

import com.bj58.hy.lib.core.Result;
import com.bj58.hy.wx.qywxbiz.service.common.monitor.MonitorDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 监控指标查询API接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/monitor/api")
public class MonitorController {

    @Autowired
    private MonitorDataService monitorDataService;

    /**
     * 获取欢迎语监控指标
     */
    @GetMapping("/welcome")
    public Result<Map<String, Object>> getWelcomeMetrics(
            @RequestParam(defaultValue = "ww5") String corpId,
            @RequestParam(defaultValue = "default") String bizScene,
            @RequestParam(defaultValue = "3") int days) {

        try {
            Map<String, Object> data = monitorDataService.getWelcomeMonitorData(corpId, bizScene, days);
            return Result.success(data);
        } catch (Exception e) {
            log.error("获取欢迎语监控指标失败", e);
            return Result.failure("获取欢迎语监控指标失败: " + e.getMessage());
        }
    }

    /**
     * 获取AI聊天监控指标
     */
    @GetMapping("/ai-chat")
    public Result<Map<String, Object>> getAiChatMetrics(
            @RequestParam(defaultValue = "ww5") String corpId,
            @RequestParam(defaultValue = "3") int days) {

        try {
            Map<String, Object> data = monitorDataService.getAiChatMonitorData(corpId, days);
            return Result.success(data);
        } catch (Exception e) {
            log.error("获取AI聊天监控指标失败", e);
            return Result.failure("获取AI聊天监控指标失败: " + e.getMessage());
        }
    }

    /**
     * 获取消息处理监控指标
     */
    @GetMapping("/message-process")
    public Result<Map<String, Object>> getMessageProcessMetrics(
            @RequestParam(defaultValue = "ww5") String corpId,
            @RequestParam(defaultValue = "3") int days) {

        try {
            Map<String, Object> data = monitorDataService.getMessageProcessMonitorData(corpId, days);
            return Result.success(data);
        } catch (Exception e) {
            log.error("获取消息处理监控指标失败", e);
            return Result.failure("获取消息处理监控指标失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户满意度监控指标
     */
    @GetMapping("/user-satisfaction")
    public Result<Map<String, Object>> getUserSatisfactionMetrics(
            @RequestParam(defaultValue = "ww5") String corpId,
            @RequestParam(defaultValue = "3") int days) {

        try {
            Map<String, Object> data = monitorDataService.getUserSatisfactionMonitorData(corpId, days);
            return Result.success(data);
        } catch (Exception e) {
            log.error("获取用户满意度监控指标失败", e);
            return Result.failure("获取用户满意度监控指标失败: " + e.getMessage());
        }
    }

    /**
     * 获取外部服务监控指标
     */
    @GetMapping("/external-service")
    public Result<Map<String, Object>> getExternalServiceMetrics(
            @RequestParam(defaultValue = "ww5") String corpId,
            @RequestParam(defaultValue = "3") int days) {

        try {
            Map<String, Object> data = monitorDataService.getExternalServiceMonitorData(corpId, days);
            return Result.success(data);
        } catch (Exception e) {
            log.error("获取外部服务监控指标失败", e);
            return Result.failure("获取外部服务监控指标失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有监控指标概览
     */
    @GetMapping("/overview")
    public Result<Map<String, Object>> getMonitorOverview(
            @RequestParam(defaultValue = "ww5") String corpId,
            @RequestParam(defaultValue = "3") int days) {

        try {
            Map<String, Object> data = monitorDataService.getMonitorOverview(corpId, days);
            return Result.success(data);
        } catch (Exception e) {
            log.error("获取监控概览失败", e);
            return Result.failure("获取监控概览失败: " + e.getMessage());
        }
    }
}
