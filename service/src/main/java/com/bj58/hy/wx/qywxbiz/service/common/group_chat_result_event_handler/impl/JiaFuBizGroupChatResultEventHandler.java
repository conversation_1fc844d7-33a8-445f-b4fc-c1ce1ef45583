package com.bj58.hy.wx.qywxbiz.service.common.group_chat_result_event_handler.impl;

import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.interfaces.scf.jiafu.GroupMemberInviteTransactionService;
import com.bj58.hy.wx.qywxbiz.service.bo.BizSceneEnum;
import com.bj58.hy.wx.qywxbiz.service.common.group_chat_result_event_handler.AbstractGroupChatResultEventHandler;
import lombok.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/11/15 10:13
 */
@Component
public class JiaFuBizGroupChatResultEventHandler extends AbstractGroupChatResultEventHandler {

    @Autowired
    private GroupMemberInviteTransactionService groupMemberInviteTransactionService;

    @Override
    public void process(@NonNull JSONObject event) {

        @NonNull String resultId = event.getString("resultId");
        @NonNull String chatId = event.getString("chatId");

        try {
            groupMemberInviteTransactionService.updateCityGroupIdsByResultId(resultId, chatId);

            log.info("更新城市群ID列表缓存成功: event = {}", event.toJSONString());

        } catch (Exception e) {
            log.error("更新城市群ID列表缓存失败: event = {}", event.toJSONString(), e);
        }
    }


    @Override
    public boolean matched(@NonNull JSONObject event) {

        if (!Objects.equals(event.getString("corpId"), "ww5cfa32107e9a1f20")) {
            return false;
        }

        if (!Objects.equals(event.getString("bizCustomizedResultFilterKey"),
                BizSceneEnum.家服_阿姨拉群加企微.getSceneName())) {
            return false;
        }

        if (ObjectUtils.isEmpty(event.getString("chatId"))) {
            return false;
        }

        if (ObjectUtils.isEmpty(event.getString("resultId"))) {
            return false;
        }

        return true;
    }


}
