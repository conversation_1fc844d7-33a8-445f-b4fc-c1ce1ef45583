package com.bj58.hy.wx.qywxbiz.service.common.welcome.dify_ai;

import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.fx.bcore.entity.neworder.OrderEntity;
import com.bj58.hy.lib.core.Result;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywx.contract.event_bo.wx_work.external_contact.AddExternalContactEventBo;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.banjia.BanJiaOrderQueryService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.cmcpc.CMCPC;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.DifyRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.WorkflowRequest;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.dify.WorkflowResponse;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.jingxuan.JingXuanOrderQueryService;
import com.bj58.hy.wx.qywxbiz.service.bo.BizSceneEnum;
import com.bj58.hy.wx.qywxbiz.service.common.dify.bo.DifyApiInfoBo;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.bo.LbgAiExternalContactStateBo;
import com.bj58.hy.wx.qywxbiz.service.common.dify.workflow.comp.DifyWorkflowAlarmComponent;
import com.bj58.hy.wx.qywxbiz.service.common.welcome.dify_ai.comp.DifyAiWelcomeConditionsComponent;
import com.bj58.hy.wx.qywxbiz.service.common.wx_work_event_handler.AbstractAddExternalContactEventHandler;
import com.bj58.hy.wx.qywxbiz.utils.DateUtils;
import com.bj58.lbg.daojia.fxbanjia.pojo.vo.order.OrderCsCDetailVO;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.NonNull;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.Executor;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
public class WxWorkEventThenDifyAiSendWelcomeMessageHandler extends AbstractAddExternalContactEventHandler {

    @Autowired
    private DifyAiWelcomeConditionsComponent aiWelcomeConditionsComponent;

    @Autowired
    private DifyRemoteService difyRemoteService;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private JingXuanOrderQueryService jingXuanOrderQueryService;

    @Autowired
    private BanJiaOrderQueryService banJiaOrderQueryService;

    @Autowired
    DifyWorkflowAlarmComponent difyWorkflowAlarmComponent;

    private final Executor executor = new ThreadPoolExecutor(4, 4,
            60L, TimeUnit.SECONDS,
            new SynchronousQueue<>(),
            new ThreadFactoryBuilder().setNameFormat("DifyAiSendWelcomeMessageHandler-%d").build(),
            new ThreadPoolExecutor.CallerRunsPolicy()
    );

    @Override
    public void process(@NonNull AddExternalContactEventBo eventMsg) {
        try {
            String stateMappedVal = eventMsg.getExt().getStateMappedVal();
            if (ObjectUtils.isEmpty(stateMappedVal)) {
                return;
            }

            // 0.判断是否符合
            if (!aiWelcomeConditionsComponent.isAiUser(
                    eventMsg.getCorpId(), eventMsg.getUserId())) {
                return;
            }

            JSONObject stateJsonObj = JSONObject.parseObject(stateMappedVal);
            if (ObjectUtils.isEmpty(stateJsonObj)) {
                return;
            }

            BizSceneEnum bizScene = BizSceneEnum.of(
                    stateJsonObj.getInteger("bizLine"),
                    stateJsonObj.getInteger("bizScene")
            );

            if (ObjectUtils.isNull(bizScene)) {
                return;
            }

            String cateName = getPreSalesCateNameMessage(stateJsonObj, bizScene);

            Long orderId = this.getOrderId(stateJsonObj);

            String orderCreateTimeStr;
            String serviceName;

            if (ObjectUtils.notEmpty(orderId)) {

                OrderEntity djjxOrder = jingXuanOrderQueryService.query(orderId);

                if (ObjectUtils.notNull(djjxOrder)) {

                    Date orderCreateTime = djjxOrder.getCreateTime();
                    orderCreateTimeStr = DateUtils.dateToStr(DateUtils.yyyyMMddHH4mmss, orderCreateTime);
                    serviceName = djjxOrder.getServiceName();

                } else {
                    OrderCsCDetailVO fxbjOrder = banJiaOrderQueryService.query(orderId);
                    if (ObjectUtils.notNull(fxbjOrder)) {

                        orderCreateTimeStr = fxbjOrder.getOrderInfo().getServiceTime();
                        serviceName = fxbjOrder.getOrderInfo().getSkuName();
                    } else {
                        serviceName = null;
                        orderCreateTimeStr = null;
                    }

                }

            } else {
                serviceName = "";
                orderCreateTimeStr = "";
            }

            // 开启线程执行
            executor.execute(() ->
                    triggerWorkflow(eventMsg.getCorpId(), eventMsg.getUserId(),
                            eventMsg.getExternalUserId(), cateName,
                            bizScene.getLineId(), bizScene.getSceneId(), orderCreateTimeStr, serviceName)
            );

        } catch (Exception e) {
            log.error("DifyAiWelcomeMessageEventHandler process error", e);
        }
    }


    public void triggerWorkflow(@NonNull final String corpId,
                                @NonNull final String corpUserId,
                                @NonNull final String externalUserId,
                                final String cateName,
                                int bizLine,
                                int bizScene,
                                String orderCreateTimeStr, String serviceName) {

        @Nullable String apiKey = getApiKey(corpId, corpUserId);
        if (ObjectUtils.isEmpty(apiKey)) {
            return;
        }

        @NonNull final DifyApiInfoBo.Item apiInfo =
                new DifyApiInfoBo.Item("程泰绮", null, apiKey);

        WorkflowRequest request = new WorkflowRequest();

        String user = String.format("%s:%s:%s", corpId, corpUserId, externalUserId);
        request.setUser(user);

        request.getInputs().put("corpId", corpId);
        request.getInputs().put("userId", corpUserId);
        request.getInputs().put("externalUserId", externalUserId);
        request.getInputs().put("cateName", cateName);
        request.getInputs().put("bizLine", String.valueOf(bizLine));
        request.getInputs().put("bizScene", String.valueOf(bizScene));
        request.getInputs().put("orderTime", orderCreateTimeStr);
        request.getInputs().put("orderName", serviceName);

        try {
            log.info("request dify ai welcome message workflow, req = {}", JacksonUtils.format(request));

            Result<WorkflowResponse> result = difyRemoteService.workflow(
                    apiKey, request);

            log.info("request dify ai welcome message workflow done, req = {}, resp = {}",
                    JacksonUtils.format(request), JacksonUtils.format(result));

            if (ObjectUtils.isNull(result) || result.isFailed() || ObjectUtils.isNull(result.getData())) {
                difyWorkflowAlarmComponent.sendTipsToMeiShi_1V1(corpId, corpUserId, apiInfo, result);
            }

        } catch (Exception e) {
            log.error("triggerWorkflow exception, workflow request = {}", JacksonUtils.format(request), e);
        }
    }

    @Nullable
    public String getPreSalesCateNameMessage(final JSONObject stateJsonObj,
                                             final BizSceneEnum bizScene) {
        String source = stateJsonObj.getString("source");
        if (ObjectUtils.isEmpty(source)) {
            return null;
        }

        LbgAiExternalContactStateBo customerAiStateMappedVal =
                JacksonUtils.parse(source, LbgAiExternalContactStateBo.class);

        Integer cateId = customerAiStateMappedVal.getCateId();
        if (ObjectUtils.isEmpty(cateId)) {
            return null;
        }

        String categoryName = CMCPC.getCategoryNameById(cateId);

        if (ObjectUtils.notEmpty(cateId)) {

            if (ObjectUtils.isEmpty(categoryName)) {
                return null;
            }

            if (BizSceneEnum.精选_售前扫码加微.equals(bizScene)) {
                RMap<String, String> mappingConfig =
                        redisson.getMap("WELCOME_MESSAGE_CATE_MAPPING_CONFIG", StringCodec.INSTANCE);

                if (ObjectUtils.notEmpty(mappingConfig)) {
                    String cateConfigName = mappingConfig.get(categoryName);
                    if (StringUtils.isNotEmpty(cateConfigName)) {
                        categoryName = cateConfigName;
                    }
                }
            }

        }

        return categoryName;
    }

    @Nullable
    public Long getOrderId(final JSONObject stateJsonObj) {
        String source = stateJsonObj.getString("source");
        if (ObjectUtils.isEmpty(source)) {
            return null;
        }

        LbgAiExternalContactStateBo customerAiStateMappedVal =
                JacksonUtils.parse(source, LbgAiExternalContactStateBo.class);


        return customerAiStateMappedVal.getOrderId();
    }

    @Nullable
    private String getApiKey(@NonNull final String corpId, @NonNull final String userId) {
        RMap<String, String> apiKeyMap = redisson.getMap(String.format("DifyWelcomeMessageWorkflowApiKeyMap:%s", corpId), StringCodec.INSTANCE);
        return apiKeyMap.get(userId);
    }

} 