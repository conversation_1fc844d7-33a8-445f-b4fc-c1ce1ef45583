package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.comp;

import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.entity.enums.NotReqAiReason;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description: 
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class LbgAiInterventionConditionsComponent {

    @Autowired
    private RedissonClient redisson;

    private static final String AI_USER_IDS = "AI_USER_IDS:%s";

    /**
     * 判断当前聊天机器人是否是当前的业务类型
     */
    public boolean isAiUser(final String corpId,
                            final String botUserId) {
        if (ObjectUtils.isEmpty(botUserId)) {
            return false;
        }

        RSet<String> aiUserIds;
        if ("Product".equals(System.getenv("WCloud_Env"))) {
            aiUserIds = redisson.getSet(String.format(AI_USER_IDS, corpId), StringCodec.INSTANCE);
        } else {
            aiUserIds = redisson.getSet(String.format(AI_USER_IDS + ":SANDBOX", corpId), StringCodec.INSTANCE);
        }

        // boolean contains = aiUserIds.contains(botUserId);
        // if (!contains) {
        //     log.warn("user id not has lbg ai auth, user id = {}", botUserId);
        // }
        // return contains;

        return aiUserIds.contains(botUserId);
    }

    /**
     * 开启当前聊天机器人AI功能
     */
    public boolean openAiByUserId(final String corpId,
                                  final String botUserId) {
        if (ObjectUtils.isEmpty(botUserId) || ObjectUtils.isEmpty(corpId)) {
            return false;
        }

        log.info("open ai auth, user id = {}", botUserId);

        RSet<String> aiUserIds = redisson.getSet(String.format(AI_USER_IDS, corpId), StringCodec.INSTANCE);
        return aiUserIds.add(botUserId);
    }

    /**
     * 开启当前聊天机器人AI功能
     */
    public boolean closeAiByUserId(final String corpId,
                                   final String botUserId) {
        if (ObjectUtils.isEmpty(botUserId) || ObjectUtils.isEmpty(corpId)) {
            return false;
        }

        log.info("close ai auth, user id = {}", botUserId);

        RSet<String> aiUserIds = redisson.getSet(String.format(AI_USER_IDS, corpId), StringCodec.INSTANCE);
        return aiUserIds.remove(botUserId);
    }

    public boolean isAiWorking(@NonNull final Date date) {
        // 是否上班时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        final int curHour = calendar.get(Calendar.HOUR_OF_DAY);

        // 09:00 - 20:00
        return curHour >= 9 && curHour < 20;
    }

    /**
     * 判断是否可以提交 AI 回复 任务，同时设置下一次可以提交AI回复任务的时间
     */
    public boolean tryGetCommitAiReplyTaskPermits(@NonNull final String corpId,
                                                  @NonNull final String userId,
                                                  @NonNull final String externalUserId,
                                                  @NonNull final Date timestamp) {

        String key = String.format("CouldCommitAiReplyTask:%s:%s:%s", corpId, userId, externalUserId);

        RAtomicLong rAtomicLong = redisson.getAtomicLong(key);
        if (!rAtomicLong.isExists()) {
            return true;
        }

        return rAtomicLong.get() <= timestamp.getTime();
    }

    /**
     * 设置可以提交AI回复任务的时间：时间戳后 可提交任务
     */
    public void setNextCommitAiReplyTaskTimestamp(@NonNull final String corpId,
                                                  @NonNull final String userId,
                                                  @NonNull final String externalUserId,
                                                  @NonNull final Date timestamp) {

        String key = String.format("CouldCommitAiReplyTask:%s:%s:%s", corpId, userId, externalUserId);

        RAtomicLong rAtomicLong = redisson.getAtomicLong(key);
        rAtomicLong.set(timestamp.getTime());
    }


    /**
     * 尝试获取 AI 托管的 权限
     */
    public boolean tryGetAiCanJoinPermits(@NonNull final String corpId,
                                          @NonNull final String userId,
                                          @NonNull final String externalUserId,
                                          @NonNull final Date timestamp) {
        String key = String.format("AiCanJoin:%s:%s:%s", corpId, userId, externalUserId);

        RAtomicLong rAtomicLong = redisson.getAtomicLong(key);
        if (!rAtomicLong.isExists()) {
            return true;
        }

        return rAtomicLong.get() <= timestamp.getTime();
    }

    /**
     * 获取AI不能介入的原因~
     */
    public NotReqAiReason getAiCanNotJoinReason(@NonNull final String corpId,
                                                @NonNull final String userId,
                                                @NonNull final String externalUserId) {
        String key = String.format("AiNotJoinReason:%s:%s:%s", corpId, userId, externalUserId);

        RBucket<String> rBucket = redisson.getBucket(key, StringCodec.INSTANCE);
        String reason = rBucket.get();

        if (Objects.equals(reason, "人工客服回复")) {
            return NotReqAiReason.MANUAL_REPLY_CANCEL_HOSTING;
        } else if (Objects.equals(reason, "AI无法承接")) {
            return NotReqAiReason.AI_CAN_NOT_JOIN_CANCEL_HOSTING;
        } else if (Objects.equals(reason, "用户要求转人工")) {
            return NotReqAiReason.USER_FORCE_REQUEST_CANCEL_HOSTING;
        } else {
            return NotReqAiReason.CANCEL_HOSTING;
        }
    }

    /**
     * 设置 AI 介入的时间：时间戳后 AI 托管
     */
    public void setNextAiCanJoinTimestamp(@NonNull final String corpId,
                                          @NonNull final String userId,
                                          @NonNull final String externalUserId,
                                          @NonNull final Date timestamp,
                                          @NonNull final String reason) {

        String key1 = String.format("AiCanJoin:%s:%s:%s", corpId, userId, externalUserId);
        RAtomicLong rAtomicLong = redisson.getAtomicLong(key1);
        rAtomicLong.set(timestamp.getTime());


        long second = (timestamp.getTime() - System.currentTimeMillis()) / 1000;
        second = Long.max(second, 30) + 5;
        String key2 = String.format("AiNotJoinReason:%s:%s:%s", corpId, userId, externalUserId);
        RBucket<String> rBucket = redisson.getBucket(key2, StringCodec.INSTANCE);
        rBucket.set(reason, second, TimeUnit.SECONDS);
    }
}
