package com.bj58.hy.wx.qywxbiz.interfaces.job.jingxuan;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.wx.qywxbiz.entity.AccountConfOperatorLogEntity;
import com.bj58.hy.wx.qywxbiz.entity.WxWorkContactWayBizAccountConfEntity;
import com.bj58.hy.wx.qywxbiz.repository.AccountConfOperatorLogRepository;
import com.bj58.hy.wx.qywxbiz.repository.WxWorkContactWayBizAccountConfRepository;
import com.bj58.hy.wx.qywxbiz.service.bo.BizSceneEnum;
import com.bj58.hy.wx.qywxbiz.utils.DateUtils;
import com.bj58.job.core.biz.model.ReturnT;
import com.bj58.job.core.handler.IJobHandler;
import com.bj58.job.core.handler.annotation.JobHandler;
import com.google.common.collect.Sets;
import com.querydsl.core.QueryResults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
@JobHandler(value = "AuntQywxConfigAutoUpdateJobHandler")
public class AuntQywxConfigAutoUpdateJobHandler extends IJobHandler {

    @Autowired
    protected WxWorkContactWayBizAccountConfRepository contactWayBizAccountConfRepository;

    @Autowired
    private AccountConfOperatorLogRepository accountConfOperatorLogRepository;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        if (StringUtils.isBlank(param)) {
            return ReturnT.FAIL;
        }
        JSONObject jobParam = JSONObject.parseObject(param);
        if (ObjectUtils.isEmpty(jobParam)) {
            return ReturnT.FAIL;
        }

        String bizLine = jobParam.getString("bizLine");
        JSONArray bizScene = jobParam.getJSONArray("bizScene");
        String copId = jobParam.getString("copId");
        if (ObjectUtils.isEmpty(bizScene) || StringUtils.isBlank(bizLine) || StringUtils.isBlank(copId)) {
            return ReturnT.FAIL;
        }
        int pageNum = 1;
        int pageSize = 100;
        QueryResults<WxWorkContactWayBizAccountConfEntity> paged = contactWayBizAccountConfRepository.listUserByParams(Integer.valueOf(bizLine),
                Sets.newHashSet(bizScene.toJavaList(Integer.class)), copId, null, null, null, null, null, 0,null,
                pageNum, pageSize);
        Date curDate = new Date();
        while (!paged.isEmpty()) {
            for (WxWorkContactWayBizAccountConfEntity result : paged.getResults()) {
                result.setUpdateTime(curDate);
                result.setState(1);
                contactWayBizAccountConfRepository.save(result);
                AccountConfOperatorLogEntity log = new AccountConfOperatorLogEntity();
                log.setOperator("系统");
                log.setAccountConfId(result.getId());
                log.setLog(DateUtils.dateToStr(DateUtils.yyyyMMddHHmmss, curDate) + System.lineSeparator()
                        + "系统操作" + BizSceneEnum.strictOf(result.getBizLine(), result.getBizScene()).getSceneName() + System.lineSeparator()
                        + "账户：" + result.getUserId() + "由待上线修改为已上线");
                log.setCreateTime(curDate);
                log.setUpdateTime(curDate);
                accountConfOperatorLogRepository.save(log);
            }
            pageNum = pageNum + 1;
            paged = contactWayBizAccountConfRepository.listUserByParams(Integer.valueOf(bizLine),
                    Sets.newHashSet(bizScene.toJavaList(Integer.class)), copId, null, null, null, null, null, 0,null,
                    pageNum, pageSize);
        }

        return ReturnT.SUCCESS;
    }

}
