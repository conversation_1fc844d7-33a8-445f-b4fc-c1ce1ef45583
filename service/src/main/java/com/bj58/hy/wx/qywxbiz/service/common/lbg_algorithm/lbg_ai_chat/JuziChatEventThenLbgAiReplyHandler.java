package com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bj58.hy.lib.core.util.JacksonUtils;
import com.bj58.hy.lib.core.util.ObjectUtils;
import com.bj58.hy.lib.spring.support.redis.RedisLockSupport;
import com.bj58.hy.wx.qywx.contract.dto.external_contact.GetExternalContactRelationshipResp;
import com.bj58.hy.wx.qywx.contract.dto.message.JuziSingleChatContentRecord;
import com.bj58.hy.wx.qywx.contract.dto.message.MessagePayload;
import com.bj58.hy.wx.qywx.contract.enums.SingleChatSendBy;
import com.bj58.hy.wx.qywx.contract.event_bo.juzi.ChatMessagesEventBo;
import com.bj58.hy.wx.qywxbiz.entity.JuziSingleChatContentRecordAiBizInfo;
import com.bj58.hy.wx.qywxbiz.entity.enums.AiType;
import com.bj58.hy.wx.qywxbiz.entity.enums.NotReqAiReason;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ChatContentRemoteService;
import com.bj58.hy.wx.qywxbiz.infrastructure.remote_service.ExternalContactRemoteService;
import com.bj58.hy.wx.qywxbiz.service.bo.BizLineEnum;
import com.bj58.hy.wx.qywxbiz.service.bo.BizSceneEnum;
import com.bj58.hy.wx.qywxbiz.service.common.juzi_event_handler.AbstractJuziChatEventHandler;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.bo.LbgAiMessageChatRecordBo;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.comp.LbgAiInterventionConditionsComponent;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.comp.LbgAiSendMessageComp;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.constants.LbgAiCustomerConstants;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.enums.MessageMarkEnum;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.LbgAiChatIdRepository;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.LbgAiChatMessageRecordRepository;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.LbgAiExternalUserRecentMessageRecordRepository;
import com.bj58.hy.wx.qywxbiz.service.common.lbg_algorithm.lbg_ai_chat.repository.LbgAiResultRepository;
import com.bj58.hy.wx.qywxbiz.service.common.monitor.AsyncMonitorService;
import com.bj58.hy.wx.qywxbiz.utils.DateUtils;
import lombok.NonNull;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
public class JuziChatEventThenLbgAiReplyHandler extends AbstractJuziChatEventHandler {

    @Autowired
    private ExternalContactRemoteService externalContactRemoteService;

    @Autowired
    private LbgAiAsyncReplyHandler asyncReplyHandler;

    @Autowired
    private LbgAiInterventionConditionsComponent aiInterventionConditionsComponent;

    @Autowired
    private LbgAiSendMessageComp sendMessageComp;

    @Autowired
    private LbgAiExternalUserRecentMessageRecordRepository externalUserRecentChatMessageRecordRepository;

    @Autowired
    private LbgAiChatMessageRecordRepository aiChatMessageRecordRepository;

    @Autowired
    private LbgAiChatIdRepository aiChatIdRepository;

    @Autowired
    private LbgAiResultRepository aiResultRepository;

    @Autowired
    private ChatContentRemoteService chatContentRemoteService;

    @Autowired
    protected RedissonClient redisson;

    @Autowired
    protected RedisLockSupport lockSupport;

    @Autowired
    private AsyncMonitorService asyncMonitorService;

    @Override
    public void process(@NonNull final ChatMessagesEventBo.Body event) {
        long startTime = System.currentTimeMillis();

        // 是否标准化了聊天记录?
        JuziSingleChatContentRecord singleChatContentRecord = getCurrentSingleChatContentRecord();
        if (ObjectUtils.isNull(singleChatContentRecord)) {
            return;
        }

        String corpId = singleChatContentRecord.getCorpId();
        String messageType = singleChatContentRecord.getSendBy().name();

        // 异步记录消息处理总量
        asyncMonitorService.recordChatMessageTotal(corpId, messageType);

        // 是否是 AI能承接的 客服+外部联系人 聊天
        if (!checkCanBeAiJoin(singleChatContentRecord)) {
            return;
        }

        // 存储聊天记录
        saveAiMessageChatRecord(singleChatContentRecord);

        if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.EXTERNAL_USER)) {
            // 监听用户发送的消息，尝试AI应答

            String botUserId = singleChatContentRecord.getUserId();
            String externalUserId = singleChatContentRecord.getExternalUserId();

            // 提交延迟收集反馈任务
            commitDelayCheckFeedbackTask(singleChatContentRecord);

            // 判断AI是否在线
            if (!aiInterventionConditionsComponent.isAiWorking(singleChatContentRecord.getCreateTime())) {
                recordNotReqAiBizInfo(singleChatContentRecord, NotReqAiReason.NOT_ONLINE_TIME);
                String message = String.format(LbgAiCustomerConstants.AI_TO_USER_MESSAGE, event.getContactName());
                sendMessageComp.sendTextToUser(corpId, botUserId, message);
                return;
            }

            // 检查并处理用户首次咨询场景
            boolean sentFirstConsultResponse = sendFirstConsultResponseMessage(singleChatContentRecord);

            // 异步记录首次咨询承接情况
            if (sentFirstConsultResponse) {
                asyncMonitorService.recordFirstConsultTakeoverSuccess(corpId);
            }

            // 满足指定条件时，如果是客户询问，则自动回复一个应答语
            // 只有在没有发送首次咨询消息的情况下才发送快速回复
            if (!sentFirstConsultResponse) {
                sendQuickResponseMessage(singleChatContentRecord);
            }

            // 判断托管状态是否正常
            if (!isAiOnline(singleChatContentRecord)) {
                NotReqAiReason reason = aiInterventionConditionsComponent.getAiCanNotJoinReason(
                        corpId, botUserId, externalUserId);
                recordNotReqAiBizInfo(singleChatContentRecord, reason);
                return;
            }

            if (Objects.equals(event.getMessageType(), 7) ||
                    Objects.equals(event.getMessageType(), 2)) {
                // 目前AI仅可回复文本消息

                @NonNull final LbgAiMessageChatRecordBo messageRecord = buildAiMessageChatRecordBo(singleChatContentRecord);

                // 存储聊天记录，供30S后聚合聊天信息给到AI以回复
                externalUserRecentChatMessageRecordRepository.saveRecentChatMessages(
                        corpId, botUserId, externalUserId, messageRecord);

                // 是否可以提交？
                boolean permits = aiInterventionConditionsComponent.tryGetCommitAiReplyTaskPermits(
                        corpId, botUserId, externalUserId, singleChatContentRecord.getCreateTime()
                );

                if (permits) {
                    aiInterventionConditionsComponent.setNextCommitAiReplyTaskTimestamp( // 设定下一次提交的时间
                            corpId, botUserId, externalUserId,
                            DateUtils.add(new Date(), Calendar.SECOND, 30)
                    );

                    asyncReplyHandler.commitAsyncAiReplyTask( // 提交一个延迟30s后处理的任务
                            corpId, botUserId, externalUserId,
                            event.getContactName());
                }

            } else {
                recordNotReqAiBizInfo(singleChatContentRecord, NotReqAiReason.NOT_SUPPORTED_MESSAGE_TYPE);

                String messageStr = String.format(LbgAiCustomerConstants.AI_TO_STAFF_MESSAGE_VOICE, event.getContactName());
                sendMessageComp.sendTextToUser(corpId, botUserId, messageStr);
            }

        } else if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
            // 监听客服发送的消息

            // 假定当前是人工客服回复的，尝试取消AI托管状态
            trySetAiOffline(singleChatContentRecord);

            // 假定当前是AI客服回复的，尝试分析数据并且记录
            tryTagAiBizInfoIfReplyByAi(singleChatContentRecord);

            // 假定当前是客服自动发送的应答语消息，尝试分析数据并且记录
            tryTagBizInfoIfQuickResponseMessage(singleChatContentRecord);
        }

        // 异步记录消息处理延迟
        long processingTime = System.currentTimeMillis() - startTime;
        asyncMonitorService.recordMessageProcessLatency(corpId, "total", processingTime);
    }

    @Override
    public boolean matched(@NonNull final ChatMessagesEventBo.Body event) {
        // 群聊，不走AI
        return ObjectUtils.isEmpty(event.getRoomWecomChatId());
    }

    private void sendQuickResponseMessage(JuziSingleChatContentRecord singleChatContentRecord) {

        lockSupport.executeWithoutResult(
                String.format("QUICK_RESPONSE_MESSAGE_LOCK:%s:%s:%s",
                        singleChatContentRecord.getCorpId(),
                        singleChatContentRecord.getUserId(),
                        singleChatContentRecord.getExternalUserId()),
                () -> {
                    // 4分钟内是否发送过
                    String sendRecordKey = String.format("LBG_QUICK_RESPONSE_MESSAGE_SEND_RECORD:%s:%s:%s",
                            singleChatContentRecord.getCorpId(),
                            singleChatContentRecord.getUserId(),
                            singleChatContentRecord.getExternalUserId());
                    RBucket<String> sendFlagBucket = redisson.getBucket(sendRecordKey, StringCodec.INSTANCE);
                    if (sendFlagBucket.isExists()) {
                        return;
                    }

                    // 距离客户or客服最后一条消息>5分钟时且小于24h
                    String chatId = aiChatIdRepository.getChatId(singleChatContentRecord.getCorpId(),
                            singleChatContentRecord.getUserId(),
                            singleChatContentRecord.getExternalUserId());

                    // 查不到有可能之前没有聊天或者是大于24h
                    if (ObjectUtils.isEmpty(chatId)) {
                        return;
                    }

                    // 获取最后一条聊天记录
                    List<LbgAiMessageChatRecordBo> chatMessages = aiChatMessageRecordRepository.getChatMessages(chatId);
                    if (ObjectUtils.isEmpty(chatMessages)) {
                        return;
                    }
                    LbgAiMessageChatRecordBo lastMessage = chatMessages.get(chatMessages.size() - 1);
                    if (ObjectUtils.isEmpty(lastMessage)) {
                        return;
                    }

                    // 如果最后一条聊天记录，是客服发送的自动邀约，则不自动发送应答语
                    if (Objects.equals(lastMessage.getMessage(), LbgAiCustomerConstants.FEEDBACK_RECOVERY_MESSAGE)) {
                        return;
                    }

                    Long timestamp = lastMessage.getDateStamp();
                    if (ObjectUtils.isNull(timestamp)) {
                        return;
                    }

                    // 是否大于5分钟
                    long time = singleChatContentRecord.getCreateTime().getTime();
                    boolean differenceGreaterThanMinutes = DateUtils.isDifferenceGreaterThanMinutes(time, timestamp, 5);

                    if (!differenceGreaterThanMinutes) {
                        return;
                    }


                    sendMessageComp.sendTextToExternalUser(
                            singleChatContentRecord.getCorpId(),
                            singleChatContentRecord.getUserId(),
                            singleChatContentRecord.getExternalUserId(),
                            LbgAiCustomerConstants.QUICK_RESPONSE_MESSAGE, "");

                    // 放到redis标记已发送过
                    sendFlagBucket.set("1", 4, TimeUnit.MINUTES);

                    // 记录到redis, 用于后续判断是否是 快搭 消息
                    String key = String.format("LBG_QUICK_RESPONSE_MESSAGE:%s:%s:%s",
                            singleChatContentRecord.getCorpId(),
                            singleChatContentRecord.getUserId(),
                            singleChatContentRecord.getExternalUserId());
                    RBucket<String> bucket = redisson.getBucket(key, StringCodec.INSTANCE);
                    bucket.set(LbgAiCustomerConstants.QUICK_RESPONSE_MESSAGE, 1, TimeUnit.MINUTES);
                }
        );
    }

    private boolean checkCanBeAiJoin(@NonNull final JuziSingleChatContentRecord singleChatContentRecord) {

        String corpId = singleChatContentRecord.getCorpId();
        String botUserId = singleChatContentRecord.getUserId();
        String externalUserId = singleChatContentRecord.getExternalUserId();

        // 判断是否是智能客服的id
        if (!aiInterventionConditionsComponent.isAiUser(corpId, botUserId)) {
            if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.EXTERNAL_USER)) {
                recordNotReqAiBizInfo(singleChatContentRecord, NotReqAiReason.NOT_HAS_AI_AUTH);
            } else if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
                recordNotReplyByAiBizInfo(singleChatContentRecord); // 肯定不是AI回复的
            }

            return false;
        }

        GetExternalContactRelationshipResp contactRelationshipResult = externalContactRemoteService.getRelationship(
                corpId, botUserId, externalUserId);

        if (ObjectUtils.isNull(contactRelationshipResult)) {
            if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.EXTERNAL_USER)) {
                recordNotReqAiBizInfo(singleChatContentRecord, NotReqAiReason.NOT_FOUND_RELATIONSHIP);
            } else if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
                recordNotReplyByAiBizInfo(singleChatContentRecord); // 没有好友关系，肯定不是AI回复的
            }

            return false;
        }

        String mappingVal = contactRelationshipResult.getStateMappedValue();
        if (ObjectUtils.isEmpty(mappingVal)) {
            if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.EXTERNAL_USER)) {
                recordNotReqAiBizInfo(singleChatContentRecord, NotReqAiReason.MISMATCH_STATE_VALUE);
            } else if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
                recordNotReplyByAiBizInfo(singleChatContentRecord); // 渠道参数不正确，肯定不是AI回复的
            }

            return false;
        }

        JSONObject stateObj = JSONObject.parseObject(mappingVal);
        if (ObjectUtils.isEmpty(stateObj)) {
            if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.EXTERNAL_USER)) {
                recordNotReqAiBizInfo(singleChatContentRecord, NotReqAiReason.MISMATCH_STATE_VALUE);
            } else if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
                recordNotReplyByAiBizInfo(singleChatContentRecord); // 渠道参数不正确，肯定不是AI回复的
            }

            return false;
        }

        BizSceneEnum bizScene = BizSceneEnum.of(
                stateObj.getInteger("bizLine"),
                stateObj.getInteger("bizScene")
        );

        if (ObjectUtils.isNull(bizScene)) {
            if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.EXTERNAL_USER)) {
                recordNotReqAiBizInfo(singleChatContentRecord, NotReqAiReason.MISMATCH_STATE_VALUE);
            } else if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
                recordNotReplyByAiBizInfo(singleChatContentRecord); // 渠道参数不正确，肯定不是AI回复的
            }

            return false;
        }

        if (!Objects.equals(bizScene.getLineId(), BizLineEnum.精选.getCode())) {
            if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.EXTERNAL_USER)) {
                recordNotReqAiBizInfo(singleChatContentRecord, NotReqAiReason.MISMATCH_STATE_VALUE);
            } else if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
                recordNotReplyByAiBizInfo(singleChatContentRecord); // 渠道参数不正确，肯定不是AI回复的
            }

            return false;
        }

        return true;
    }

    private boolean isAiOnline(@NonNull final JuziSingleChatContentRecord singleChatContentRecord) {

        String corpId = singleChatContentRecord.getCorpId();
        String botUserId = singleChatContentRecord.getUserId();
        String externalUserId = singleChatContentRecord.getExternalUserId();
        Date eventTimestamp = singleChatContentRecord.getCreateTime();

        // 如果人工坐席 还未到期，不进行AI处理
        return aiInterventionConditionsComponent.tryGetAiCanJoinPermits(
                corpId, botUserId, externalUserId, eventTimestamp);
    }

    private void trySetAiOffline(@NonNull final JuziSingleChatContentRecord singleChatContentRecord) {

        if (!Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
            return;
        }

        // 排除SOP消息（sendSource=7）
        boolean isSopMessage = Objects.equals(singleChatContentRecord.getSendSource(), 7); // 7- sop功能

        // 如果是SOP消息，直接返回
        if (isSopMessage) {
            return;
        }

        boolean isHumanUserSendMessage = Objects.equals(singleChatContentRecord.getSendSource(), 0)  // 0- 手机推送过来的消息
                || Objects.equals(singleChatContentRecord.getSendSource(), 1); // 1- 小组级控制台手动发送消息

        if (!isHumanUserSendMessage) {
            return;
        }

        String corpId = singleChatContentRecord.getCorpId();
        String botUserId = singleChatContentRecord.getUserId();
        String externalUserId = singleChatContentRecord.getExternalUserId();

        // 设定30m内不需要AI处理
        aiInterventionConditionsComponent.setNextAiCanJoinTimestamp(
                corpId, botUserId, externalUserId,
                DateUtils.addMinute(singleChatContentRecord.getCreateTime(), 30),
                "人工客服回复"
        );
    }


    private void tryTagAiBizInfoIfReplyByAi(@NonNull final JuziSingleChatContentRecord singleChatContentRecord) {

        if (!Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
            return;
        }

        // 检查是否是SOP消息
        if (Objects.equals(singleChatContentRecord.getSendSource(), 7)) { // 7- sop功能
            // 标记为SOP消息
            chatContentRemoteService.recordSopMessage(singleChatContentRecord.getMessageId());
            return;
        }

        if (!Objects.equals(singleChatContentRecord.getSendSource(), 6)) { // 6. api发消息
            recordNotReplyByAiBizInfo(singleChatContentRecord);
            return;
        }

        if (Objects.equals(singleChatContentRecord.getMessageType(), 7)) { // 7. 文本消息
            String payload = singleChatContentRecord.getPayload();
            if (ObjectUtils.isEmpty(payload)) {
                log.error("curr chat content is text but not found content, message id = {}", singleChatContentRecord.getMessageId());
                return;
            }

            JSONObject jsonObj = JSON.parseObject(payload);
            String text = jsonObj.getString("text");
            if (ObjectUtils.isEmpty(text)) {
                log.error("curr chat content is text but not found content, message id = {}", singleChatContentRecord.getMessageId());
                return;
            }

            if (Objects.equals(text, LbgAiCustomerConstants.FEEDBACK_RECOVERY_MESSAGE)) {
                chatContentRemoteService.recordSingleChatBizInfo(singleChatContentRecord.getMessageId(), "sendUserFeedback", JacksonUtils.format(singleChatContentRecord));
            }

            String chatId = aiChatIdRepository.getChatId(
                    singleChatContentRecord.getCorpId(),
                    singleChatContentRecord.getUserId(),
                    singleChatContentRecord.getExternalUserId());

            List<String> replyMessageIds = aiResultRepository.get(chatId, text);

            if (ObjectUtils.notEmpty(replyMessageIds)) {
                recordReplyByAiBizInfo(singleChatContentRecord, replyMessageIds);
            } else {
                recordNotReplyByAiBizInfo(singleChatContentRecord);
            }

        } else if (Objects.equals(singleChatContentRecord.getMessageType(), 9)) { // 9. 小程序消息
            String payload = singleChatContentRecord.getPayload();
            if (ObjectUtils.isEmpty(payload)) {
                log.error("curr chat content is applet but not found content, message id = {}", singleChatContentRecord.getMessageId());
                return;
            }

            JSONObject jsonObj = JSON.parseObject(payload);
            String pagePath = jsonObj.getString("pagePath");
            if (ObjectUtils.isEmpty(pagePath)) {
                log.error("curr chat content is applet but not found page path, message id = {}", singleChatContentRecord.getMessageId());
                return;
            }

            String chatId = aiChatIdRepository.getChatId(
                    singleChatContentRecord.getCorpId(),
                    singleChatContentRecord.getUserId(),
                    singleChatContentRecord.getExternalUserId());

            List<String> replyMessageIds = aiResultRepository.get(chatId, pagePath);

            if (ObjectUtils.notEmpty(replyMessageIds)) {
                recordReplyByAiBizInfo(singleChatContentRecord, replyMessageIds);
            } else {
                recordNotReplyByAiBizInfo(singleChatContentRecord);
            }

        } else {

            recordNotReplyByAiBizInfo(singleChatContentRecord);

        }

    }

    private void recordNotReqAiBizInfo(@NonNull final JuziSingleChatContentRecord singleChatContentRecord,
                                       @NonNull final NotReqAiReason notReqAiReason) {

        final String messageId = singleChatContentRecord.getMessageId();

        // 当前聊天是肯定是外部联系人发送的
        if (!Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.EXTERNAL_USER)) {
            log.error("curr chat content not send by external user, message id = {}", singleChatContentRecord.getMessageId());
            return;
        }

        chatContentRemoteService.recordSingleChatBizInfo(
                messageId,
                JuziSingleChatContentRecordAiBizInfo.builder()
                        .messageId(messageId)
                        .aiType(AiType.LBG_AI)
                        .sendBy(SingleChatSendBy.EXTERNAL_USER)
                        .reqToAiBizInfo(
                                JuziSingleChatContentRecordAiBizInfo.ReqToAiBizInfo.builder()
                                        .flag(false)
                                        .notReqReason(notReqAiReason)
                                        .build()
                        )
                        .build()
        );
    }

    private void recordNotReplyByAiBizInfo(@NonNull final JuziSingleChatContentRecord singleChatContentRecord) {

        final String messageId = singleChatContentRecord.getMessageId();

        // 当前聊天是肯定是企业成员人发送的
        if (!Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
            log.error("curr chat content not send by corp user, message id = {}", singleChatContentRecord.getMessageId());
            return;
        }

        // 当前聊天是否是企业成员发送的？
        chatContentRemoteService.recordSingleChatBizInfo(
                messageId,
                JuziSingleChatContentRecordAiBizInfo.builder()
                        .messageId(messageId)
                        .aiType(AiType.LBG_AI)
                        .sendBy(SingleChatSendBy.CORP_USER)
                        .respByAiBizInfo(
                                JuziSingleChatContentRecordAiBizInfo.RespByAiBizInfo.builder()
                                        .flag(false)
                                        .build()
                        )
                        .build()
        );
    }

    private void recordReplyByAiBizInfo(@NonNull final JuziSingleChatContentRecord singleChatContentRecord,
                                        @NonNull final List<String> replyMessageIds) {

        final String messageId = singleChatContentRecord.getMessageId();

        // 当前聊天是否是外部联系人发送的？
        if (Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
            chatContentRemoteService.recordSingleChatBizInfo(
                    messageId,
                    JuziSingleChatContentRecordAiBizInfo.builder()
                            .messageId(messageId)
                            .aiType(AiType.LBG_AI)
                            .sendBy(SingleChatSendBy.CORP_USER)
                            .respByAiBizInfo(
                                    JuziSingleChatContentRecordAiBizInfo.RespByAiBizInfo.builder()
                                            .flag(true)
                                            .replyMessageIds(replyMessageIds)
                                            .build()
                            )
                            .build()
            );
        }
    }

    private void saveAiMessageChatRecord(@NonNull final JuziSingleChatContentRecord singleChatContentRecord) {

        final String corpId = singleChatContentRecord.getCorpId();
        final String botUserId = singleChatContentRecord.getUserId();
        final String externalUserId = singleChatContentRecord.getExternalUserId();

        final String chatId = aiChatIdRepository.getChatId(corpId, botUserId, externalUserId);

        String message = "";
        try {
            if (Objects.equals(singleChatContentRecord.getMessageType(), 7)) {
                // 文字
                final MessagePayload.TextPayload textPayload = JacksonUtils.parse(
                        singleChatContentRecord.getPayload(),
                        MessagePayload.TextPayload.class);
                message = textPayload.getText();
            } else if (Objects.equals(singleChatContentRecord.getMessageType(), 2)) {
                // 语音
                JSONObject payload = JSONObject.parseObject(singleChatContentRecord.getPayload());
                message = payload.getString("text");
            } else if (Objects.equals(singleChatContentRecord.getMessageType(), 6)) {
                // 图片
                JSONObject payload = JSONObject.parseObject(singleChatContentRecord.getPayload());
                // 优先获取artwork中的url，如果不存在则获取imageUrl
                JSONObject artwork = payload.getJSONObject("artwork");
                if (artwork != null && artwork.getString("url") != null) {
                    message = artwork.getString("url");
                } else {
                    message = payload.getString("imageUrl");
                }
            }
        } catch (Exception ignored) {

        }


        if (ObjectUtils.isEmpty(message)) {
            return;
        }

        final LbgAiMessageChatRecordBo messageRecord = new LbgAiMessageChatRecordBo(
                singleChatContentRecord.getCreateTime().getTime(),
                message,
                Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.EXTERNAL_USER) ? externalUserId : botUserId,
                Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.EXTERNAL_USER) ? botUserId : externalUserId,
                singleChatContentRecord.getMessageId(),
                Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.EXTERNAL_USER)
        );
        aiChatMessageRecordRepository.save(chatId, messageRecord);
    }


    private LbgAiMessageChatRecordBo buildAiMessageChatRecordBo(@NonNull final JuziSingleChatContentRecord singleChatContentRecord) {

        String text = "";
        if (Objects.equals(singleChatContentRecord.getMessageType(), 7)) {
            final MessagePayload.TextPayload textPayload = JacksonUtils.parse(
                    singleChatContentRecord.getPayload(),
                    MessagePayload.TextPayload.class);

            text = textPayload.getText();
        }

        if (Objects.equals(singleChatContentRecord.getMessageType(), 2)) {
            final MessagePayload.VoicePayload voicePayload = JacksonUtils.parse(
                    singleChatContentRecord.getPayload(),
                    MessagePayload.VoicePayload.class);

            text = voicePayload.getText();
        }

        return new LbgAiMessageChatRecordBo(
                singleChatContentRecord.getCreateTime().getTime(),
                text,
                singleChatContentRecord.getUserId(),
                singleChatContentRecord.getExternalUserId(),
                singleChatContentRecord.getMessageId(),
                true
        );
    }


    private void tryTagBizInfoIfQuickResponseMessage(@NonNull final JuziSingleChatContentRecord singleChatContentRecord) {
        try {
            if (!Objects.equals(singleChatContentRecord.getSendBy(), SingleChatSendBy.CORP_USER)) {
                return;
            }

            String payload = singleChatContentRecord.getPayload();
            if (ObjectUtils.isEmpty(payload)) {
                log.error("curr chat content is text but not found content, message id = {}", singleChatContentRecord.getMessageId());
                return;
            }

            JSONObject jsonObj = JSON.parseObject(payload);
            String text = jsonObj.getString("text");
            if (ObjectUtils.isEmpty(text)) {
                log.error("curr chat content is text but not found content, message id = {}", singleChatContentRecord.getMessageId());
                return;
            }

            String key = String.format("LBG_QUICK_RESPONSE_MESSAGE:%s:%s:%s",
                    singleChatContentRecord.getCorpId(),
                    singleChatContentRecord.getUserId(),
                    singleChatContentRecord.getExternalUserId());
            RBucket<String> bucket = redisson.getBucket(key, StringCodec.INSTANCE);

            String responseMessage = bucket.get();
            if (ObjectUtils.isEmpty(responseMessage)) {
                return;
            }

            // 校验是否和应答语匹配
            if (!Objects.equals(responseMessage, text)) {
                return;
            }

            // 记录应答语标记
            chatContentRemoteService.recordSingleChatBizInfo(
                    singleChatContentRecord.getMessageId(),
                    "LBG_MESSAGE_MARK",
                    MessageMarkEnum.QUICK_RESPONSE.getName());

        } catch (Exception e) {

            log.error("tryMarkResponseMessage error messageId : {}", singleChatContentRecord.getMessageId(), e);
        }
    }

    private void commitDelayCheckFeedbackTask(@NonNull final JuziSingleChatContentRecord singleChatContentRecord) {
        RBucket<String> bucket = redisson.getBucket("LBG_AI_NOT_SEND_DELAY_FEEDBACK_MESSAGE", StringCodec.INSTANCE);
        boolean exists = bucket.isExists();
        if (exists) {
            return;
        }

        final String corpId = singleChatContentRecord.getCorpId();
        final String userId = singleChatContentRecord.getUserId();
        final String externalUserId = singleChatContentRecord.getExternalUserId();

        asyncReplyHandler.commitDelayCheckFeedbackTask(corpId, userId, externalUserId, singleChatContentRecord);
    }

    private boolean sendFirstConsultResponseMessage(JuziSingleChatContentRecord singleChatContentRecord) {
        try {
            String corpId = singleChatContentRecord.getCorpId();
            String botUserId = singleChatContentRecord.getUserId();
            String externalUserId = singleChatContentRecord.getExternalUserId();

            // 检查是否是首次咨询（通过Redis判断是否已经发送过承接语）
            String firstConsultKey = String.format("FIRST_CONSULT_RESPONSE:%s:%s:%s",
                    corpId, botUserId, externalUserId);
            RBucket<String> firstConsultBucket = redisson.getBucket(firstConsultKey, StringCodec.INSTANCE);

            // 如果已经发送过首次咨询响应，则不再发送
            if (firstConsultBucket.isExists()) {
                return false;
            }

            // 获取聊天ID
            String chatId = aiChatIdRepository.getChatId(corpId, botUserId, externalUserId);

            // 查不到chatId，可能是首次聊天
            if (ObjectUtils.isEmpty(chatId)) {
                log.info("用户首次聊天，无法获取chatId, corpId={}, botUserId={}, externalUserId={}",
                        corpId, botUserId, externalUserId);

                // 发送首次咨询承接语
                return sendFirstConsultResponse(corpId, botUserId, externalUserId, firstConsultBucket);
            }

            // 获取历史聊天记录
            List<LbgAiMessageChatRecordBo> chatMessages = aiChatMessageRecordRepository.getChatMessages(chatId);

            // 没有历史聊天记录，或者只有当前消息（首次聊天）
            if (ObjectUtils.isEmpty(chatMessages) || chatMessages.size() <= 1) {
                log.info("用户首次发送消息，chatMessages size={}, corpId={}, botUserId={}, externalUserId={}",
                        ObjectUtils.isEmpty(chatMessages) ? 0 : chatMessages.size(),
                        corpId, botUserId, externalUserId);

                // 发送首次咨询承接语
                return sendFirstConsultResponse(corpId, botUserId, externalUserId, firstConsultBucket);
            }

            // 如果当前用户是首次发消息（不算加好友和系统消息）
            boolean isFirstUserMessage = true;
            for (LbgAiMessageChatRecordBo message : chatMessages) {
                // 排除当前消息和系统消息，检查是否有用户之前发送的消息
                if (!message.getMessageId().equals(singleChatContentRecord.getMessageId()) &&
                        message.getIsUserSend()) {
                    isFirstUserMessage = false;
                    break;
                }
            }

            if (isFirstUserMessage) {
                log.info("用户首次主动发送消息，corpId={}, botUserId={}, externalUserId={}",
                        corpId, botUserId, externalUserId);

                // 发送首次咨询承接语
                return sendFirstConsultResponse(corpId, botUserId, externalUserId, firstConsultBucket);
            }

            // 没有发送首次咨询消息
            return false;

        } catch (Exception e) {

            log.error("sendFirstConsultResponseMessage error messageId : {}", singleChatContentRecord.getMessageId(), e);
            return false;
        }
    }

    // 提取发送首次咨询响应的公共方法
    private boolean sendFirstConsultResponse(@NonNull final String corpId,
                                             @NonNull final String botUserId,
                                             @NonNull final String externalUserId,
                                             @NonNull final RBucket<String> firstConsultBucket) {

        // 发送首次咨询承接语
        sendMessageComp.sendTextToExternalUser(corpId,
                botUserId,
                externalUserId,
                LbgAiCustomerConstants.FIRST_CONSULT_RESPONSE_MESSAGE, "");

        // 记录已发送首次咨询响应状态，永久保存
        firstConsultBucket.set("1");

        log.info("发送首次咨询承接语成功, corpId={}, botUserId={}, externalUserId={}",
                corpId, botUserId, externalUserId);

        return true;
    }

}
