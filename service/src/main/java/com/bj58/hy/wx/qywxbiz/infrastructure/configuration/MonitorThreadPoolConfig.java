package com.bj58.hy.wx.qywxbiz.infrastructure.configuration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 监控线程池配置
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableAsync
public class MonitorThreadPoolConfig {

    /**
     * 监控专用线程池
     * 用于异步执行监控逻辑，避免影响主业务流程
     */
    @Bean("monitorExecutor")
    public Executor monitorExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数：监控任务通常不会太多，设置较小的核心线程数
        executor.setCorePoolSize(2);
        
        // 最大线程数：监控任务突增时的最大线程数
        executor.setMaxPoolSize(8);
        
        // 队列容量：监控任务的队列大小
        executor.setQueueCapacity(1000);
        
        // 线程名前缀
        executor.setThreadNamePrefix("Monitor-");
        
        // 线程空闲时间：60秒后回收空闲线程
        executor.setKeepAliveSeconds(60);
        
        // 拒绝策略：监控任务不是关键任务，直接丢弃不影响主业务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                log.warn("监控任务被拒绝执行，当前线程池状态: 核心线程数={}, 活跃线程数={}, 队列大小={}", 
                    e.getCorePoolSize(), e.getActiveCount(), e.getQueue().size());
                super.rejectedExecution(r, e);
            }
        });
        
        // 等待所有任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间：最多等待30秒
        executor.setAwaitTerminationSeconds(30);
        
        executor.initialize();
        
        log.info("监控线程池初始化完成: 核心线程数={}, 最大线程数={}, 队列容量={}", 
            executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getThreadPoolExecutor().getQueue().size());
        
        return executor;
    }
}
