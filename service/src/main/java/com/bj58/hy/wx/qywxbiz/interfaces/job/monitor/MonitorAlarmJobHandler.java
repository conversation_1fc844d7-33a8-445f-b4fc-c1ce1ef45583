package com.bj58.hy.wx.qywxbiz.interfaces.job.monitor;

import com.bj58.hy.wx.qywxbiz.service.common.monitor.MonitorAlarmService;
import com.bj58.job.core.biz.model.ReturnT;
import com.bj58.job.core.handler.IJobHandler;
import com.bj58.job.core.handler.annotation.JobHandler;
import com.bj58.job.core.log.WJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 监控告警定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@JobHandler(value = "monitorAlarmJobHandler")
public class MonitorAlarmJobHandler extends IJobHandler {

    @Autowired
    private MonitorAlarmService monitorAlarmService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        WJobLogger.log("开始执行监控告警检查任务");

        try {
            // 获取需要监控的企业ID列表
            List<String> corpIds = getMonitoredCorpIds();
            WJobLogger.log("本次检查企业数量: " + corpIds.size());

            // 批量检查所有企业的监控告警
            String[] corpIdArray = corpIds.toArray(new String[0]);
            monitorAlarmService.checkAllAlarmsForCorps(corpIdArray);

            WJobLogger.log("监控告警检查任务执行完成，共检查 " + corpIds.size() + " 个企业");

        } catch (Exception e) {
            log.error("监控告警检查任务执行失败", e);
            WJobLogger.log("监控告警检查任务执行失败: " + e.getMessage());
            throw e;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 获取需要监控的企业ID列表
     * 实际实现中可以从数据库或配置文件中获取
     *
     * @return 企业ID列表
     */
    private List<String> getMonitoredCorpIds() {
        // 这里可以从数据库查询活跃的企业ID
        // 或者从Redis配置中获取需要监控的企业ID
        return Arrays.asList(
                "ww5cfa32107e9a1f20", // 精选客服企业ID
                "default_corp_1",
                "default_corp_2"
        );
    }
}
