<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企微客服监控系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .welcome-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .btn-custom {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="welcome-card p-5">
                    <!-- 标题区域 -->
                    <div class="text-center mb-5">
                        <h1 class="display-4 fw-bold text-primary mb-3">
                            <i class="fas fa-chart-line me-3"></i>
                            企微客服监控系统
                        </h1>
                        <p class="lead text-muted">实时监控企微客服系统各项关键指标，确保服务质量</p>
                    </div>

                    <!-- 功能卡片区域 -->
                    <div class="row g-4 mb-5">
                        <div class="col-md-6 col-lg-4">
                            <div class="card feature-card h-100 text-center p-4">
                                <div class="card-body">
                                    <i class="fas fa-tachometer-alt feature-icon text-primary"></i>
                                    <h5 class="card-title">监控概览</h5>
                                    <p class="card-text text-muted">查看所有监控指标的综合概览，快速了解系统整体状态</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="card feature-card h-100 text-center p-4">
                                <div class="card-body">
                                    <i class="fas fa-comments feature-icon text-success"></i>
                                    <h5 class="card-title">欢迎语监控</h5>
                                    <p class="card-text text-muted">监控欢迎语生成和发送情况，包括成功率、重试次数等</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="card feature-card h-100 text-center p-4">
                                <div class="card-body">
                                    <i class="fas fa-robot feature-icon text-info"></i>
                                    <h5 class="card-title">AI聊天监控</h5>
                                    <p class="card-text text-muted">监控AI调用成功率、响应时间、承接率等关键指标</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="card feature-card h-100 text-center p-4">
                                <div class="card-body">
                                    <i class="fas fa-envelope feature-icon text-warning"></i>
                                    <h5 class="card-title">消息处理监控</h5>
                                    <p class="card-text text-muted">监控消息处理总量、延迟、首次咨询承接率等</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="card feature-card h-100 text-center p-4">
                                <div class="card-body">
                                    <i class="fas fa-smile feature-icon text-danger"></i>
                                    <h5 class="card-title">用户满意度监控</h5>
                                    <p class="card-text text-muted">监控用户满意度调查、反馈回收等用户体验指标</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-4">
                            <div class="card feature-card h-100 text-center p-4">
                                <div class="card-body">
                                    <i class="fas fa-external-link-alt feature-icon text-secondary"></i>
                                    <h5 class="card-title">外部服务监控</h5>
                                    <p class="card-text text-muted">监控Dify API、订单查询等外部服务的调用情况</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 快速开始区域 -->
                    <div class="text-center">
                        <h3 class="mb-4">快速开始</h3>
                        <div class="row justify-content-center">
                            <div class="col-md-8">
                                <p class="text-muted mb-4">查看精选客服监控数据</p>
                                <div class="d-flex justify-content-center gap-3 flex-wrap">
                                    <form action="/monitor/view/dashboard" method="get">
                                        <input type="hidden" name="corpId" value="ww5cfa32107e9a1f20">
                                        <input type="hidden" name="days" value="3">
                                        <button type="submit" class="btn btn-custom">
                                            <i class="fas fa-tachometer-alt me-2"></i>
                                            统一监控仪表板
                                        </button>
                                    </form>
                                    <form action="/monitor/view/overview" method="get">
                                        <input type="hidden" name="corpId" value="ww5cfa32107e9a1f20">
                                        <input type="hidden" name="days" value="3">
                                        <button type="submit" class="btn btn-outline-primary">
                                            <i class="fas fa-chart-line me-2"></i>
                                            传统监控页面
                                        </button>
                                    </form>
                                    <a href="/monitor/view/data-generator" class="btn btn-outline-secondary">
                                        <i class="fas fa-database me-2"></i>
                                        生成测试数据
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统信息 -->
                    <div class="row mt-5 pt-4 border-top">
                        <div class="col-md-4 text-center">
                            <i class="fas fa-database text-primary mb-2" style="font-size: 2rem;"></i>
                            <h6>数据存储</h6>
                            <p class="text-muted small">Redis缓存，3天数据保留</p>
                        </div>
                        <div class="col-md-4 text-center">
                            <i class="fas fa-sync-alt text-success mb-2" style="font-size: 2rem;"></i>
                            <h6>实时更新</h6>
                            <p class="text-muted small">异步监控，不影响业务性能</p>
                        </div>
                        <div class="col-md-4 text-center">
                            <i class="fas fa-bell text-warning mb-2" style="font-size: 2rem;"></i>
                            <h6>智能告警</h6>
                            <p class="text-muted small">自动检测异常，及时通知</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
