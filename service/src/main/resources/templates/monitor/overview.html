<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:replace="~{monitor/layout :: html}">
<head>
    <title>监控概览 - 企微客服监控系统</title>
</head>
<body>
    <div th:fragment="content">
        <div th:if="${error}" class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span th:text="${error}">错误信息</span>
        </div>

        <div th:unless="${error}">
            <!-- 关键指标卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card metric-card success">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title text-muted">欢迎语生成成功率</h6>
                                    <h3 class="text-success" th:text="${overviewData != null and overviewData.welcome != null ? #numbers.formatDecimal(overviewData.welcome.generateSuccessRate, 1, 2) + '%' : 'N/A'}">95.5%</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-comments fa-2x text-success"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card" th:classappend="${overviewData != null and overviewData.aiChat != null and overviewData.aiChat.syncSuccessRate >= 90} ? 'success' : 'warning'">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title text-muted">AI调用成功率</h6>
                                    <h3 th:class="${overviewData != null and overviewData.aiChat != null and overviewData.aiChat.syncSuccessRate >= 90} ? 'text-success' : 'text-warning'"
                                        th:text="${overviewData != null and overviewData.aiChat != null ? #numbers.formatDecimal(overviewData.aiChat.syncSuccessRate, 1, 2) + '%' : 'N/A'}">92.3%</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-robot fa-2x" th:class="${overviewData != null and overviewData.aiChat != null and overviewData.aiChat.syncSuccessRate >= 90} ? 'text-success' : 'text-warning'"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card" th:classappend="${overviewData != null and overviewData.messageProcess != null and overviewData.messageProcess.firstConsultSuccessRate >= 85} ? 'success' : 'danger'">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title text-muted">首次咨询承接率</h6>
                                    <h3 th:class="${overviewData != null and overviewData.messageProcess != null and overviewData.messageProcess.firstConsultSuccessRate >= 85} ? 'text-success' : 'text-danger'"
                                        th:text="${overviewData != null and overviewData.messageProcess != null ? #numbers.formatDecimal(overviewData.messageProcess.firstConsultSuccessRate, 1, 2) + '%' : 'N/A'}">87.8%</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-envelope fa-2x" th:class="${overviewData != null and overviewData.messageProcess != null and overviewData.messageProcess.firstConsultSuccessRate >= 85} ? 'text-success' : 'text-danger'"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card" th:classappend="${overviewData != null and overviewData.userSatisfaction != null and overviewData.userSatisfaction.satisfactionRate >= 60} ? 'success' : 'danger'">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title text-muted">用户满意度</h6>
                                    <h3 th:class="${overviewData != null and overviewData.userSatisfaction != null and overviewData.userSatisfaction.satisfactionRate >= 60} ? 'text-success' : 'text-danger'"
                                        th:text="${overviewData != null and overviewData.userSatisfaction != null ? #numbers.formatDecimal(overviewData.userSatisfaction.satisfactionRate, 1, 2) + '%' : 'N/A'}">72.1%</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-smile fa-2x" th:class="${overviewData != null and overviewData.userSatisfaction != null and overviewData.userSatisfaction.satisfactionRate >= 60} ? 'text-success' : 'text-danger'"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="row mb-4">
                <!-- 欢迎语趋势图 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-comments me-2"></i>
                                欢迎语生成趋势
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="welcomeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI调用趋势图 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-robot me-2"></i>
                                AI调用趋势
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="aiChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <!-- 消息处理趋势图 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-envelope me-2"></i>
                                消息处理趋势
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="messageChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 外部服务调用趋势图 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-external-link-alt me-2"></i>
                                外部服务调用趋势
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="externalChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速链接 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-link me-2"></i>
                                快速导航
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-2">
                                    <a th:href="@{/monitor/view/welcome(corpId=${corpId}, days=${days})}" class="btn btn-outline-primary w-100 mb-2">
                                        <i class="fas fa-comments me-2"></i>
                                        欢迎语监控
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a th:href="@{/monitor/view/ai-chat(corpId=${corpId}, days=${days})}" class="btn btn-outline-info w-100 mb-2">
                                        <i class="fas fa-robot me-2"></i>
                                        AI聊天监控
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a th:href="@{/monitor/view/message-process(corpId=${corpId}, days=${days})}" class="btn btn-outline-warning w-100 mb-2">
                                        <i class="fas fa-envelope me-2"></i>
                                        消息处理监控
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a th:href="@{/monitor/view/user-satisfaction(corpId=${corpId}, days=${days})}" class="btn btn-outline-danger w-100 mb-2">
                                        <i class="fas fa-smile me-2"></i>
                                        用户满意度监控
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <a th:href="@{/monitor/view/external-service(corpId=${corpId}, days=${days})}" class="btn btn-outline-secondary w-100 mb-2">
                                        <i class="fas fa-external-link-alt me-2"></i>
                                        外部服务监控
                                    </a>
                                </div>
                                <div class="col-md-2">
                                    <button onclick="location.reload()" class="btn btn-outline-success w-100 mb-2">
                                        <i class="fas fa-sync-alt me-2"></i>
                                        刷新数据
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script th:unless="${error}">
            // 准备图表数据
            const dates = /*[[${overviewData != null and overviewData.welcome != null and overviewData.welcome.generateSuccessData != null ? #maps.keys(overviewData.welcome.generateSuccessData) : {}}]]*/ [];

            // 欢迎语趋势图
            const welcomeSuccessData = /*[[${overviewData != null and overviewData.welcome != null and overviewData.welcome.generateSuccessData != null ? #maps.values(overviewData.welcome.generateSuccessData) : {}}]]*/ [];
            const welcomeFailureData = /*[[${overviewData != null and overviewData.welcome != null and overviewData.welcome.generateFailureData != null ? #maps.values(overviewData.welcome.generateFailureData) : {}}]]*/ [];
            
            createLineChart('welcomeChart', dates, [
                {
                    label: '生成成功',
                    data: welcomeSuccessData,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                },
                {
                    label: '生成失败',
                    data: welcomeFailureData,
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4
                }
            ]);

            // AI调用趋势图
            const aiSuccessData = /*[[${overviewData != null and overviewData.aiChat != null and overviewData.aiChat.syncSuccessData != null ? #maps.values(overviewData.aiChat.syncSuccessData) : {}}]]*/ [];
            const aiFailureData = /*[[${overviewData != null and overviewData.aiChat != null and overviewData.aiChat.syncFailureData != null ? #maps.values(overviewData.aiChat.syncFailureData) : {}}]]*/ [];
            
            createLineChart('aiChart', dates, [
                {
                    label: 'AI调用成功',
                    data: aiSuccessData,
                    borderColor: '#17a2b8',
                    backgroundColor: 'rgba(23, 162, 184, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'AI调用失败',
                    data: aiFailureData,
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4
                }
            ]);

            // 消息处理趋势图
            const userMessageData = /*[[${overviewData != null and overviewData.messageProcess != null and overviewData.messageProcess.userMessageData != null ? #maps.values(overviewData.messageProcess.userMessageData) : {}}]]*/ [];
            const botMessageData = /*[[${overviewData != null and overviewData.messageProcess != null and overviewData.messageProcess.botMessageData != null ? #maps.values(overviewData.messageProcess.botMessageData) : {}}]]*/ [];
            
            createLineChart('messageChart', dates, [
                {
                    label: '用户消息',
                    data: userMessageData,
                    borderColor: '#ffc107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    tension: 0.4
                },
                {
                    label: '机器人消息',
                    data: botMessageData,
                    borderColor: '#6f42c1',
                    backgroundColor: 'rgba(111, 66, 193, 0.1)',
                    tension: 0.4
                }
            ]);

            // 外部服务调用趋势图
            const difySuccessData = /*[[${overviewData != null and overviewData.externalService != null and overviewData.externalService.difySuccessData != null ? #maps.values(overviewData.externalService.difySuccessData) : {}}]]*/ [];
            const difyFailureData = /*[[${overviewData != null and overviewData.externalService != null and overviewData.externalService.difyFailureData != null ? #maps.values(overviewData.externalService.difyFailureData) : {}}]]*/ [];
            
            createLineChart('externalChart', dates, [
                {
                    label: 'Dify调用成功',
                    data: difySuccessData,
                    borderColor: '#20c997',
                    backgroundColor: 'rgba(32, 201, 151, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'Dify调用失败',
                    data: difyFailureData,
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4
                }
            ]);

            // 设置页面标题
            document.title = '监控概览 - 企微客服监控系统';
            
            // 30秒后自动刷新
            autoRefresh(30);
        </script>
    </div>
</body>
</html>
