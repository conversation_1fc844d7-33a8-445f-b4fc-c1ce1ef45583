<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:replace="~{monitor/layout :: html}">
<head>
    <title>错误 - 企微客服监控系统</title>
</head>
<body>
    <div th:fragment="content">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card border-danger">
                    <div class="card-header bg-danger text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            系统错误
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <i class="fas fa-exclamation-circle text-danger" style="font-size: 4rem;"></i>
                        </div>
                        <div class="alert alert-danger" role="alert">
                            <strong>错误信息：</strong>
                            <span th:text="${error} ?: '未知错误'">获取监控数据失败</span>
                        </div>
                        <div class="text-center">
                            <p class="text-muted mb-4">请稍后重试，或联系系统管理员</p>
                            <div class="btn-group" role="group">
                                <button onclick="history.back()" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    返回上页
                                </button>
                                <button onclick="location.reload()" class="btn btn-primary">
                                    <i class="fas fa-sync-alt me-2"></i>
                                    重新加载
                                </button>
                                <a href="/monitor/view/" class="btn btn-success">
                                    <i class="fas fa-home me-2"></i>
                                    返回首页
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
