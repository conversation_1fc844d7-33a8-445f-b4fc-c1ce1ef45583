<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:replace="~{monitor/layout :: html}">
<head>
    <title>欢迎语监控 - 企微客服监控系统</title>
</head>
<body>
    <div th:fragment="content">
        <div th:if="${error}" class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span th:text="${error}">错误信息</span>
        </div>

        <div th:unless="${error}">
            <!-- 关键指标卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card metric-card" th:classappend="${welcomeData.generateSuccessRate >= 95} ? 'success' : 'warning'">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title text-muted">生成成功率</h6>
                                    <h3 th:class="${welcomeData.generateSuccessRate >= 95} ? 'text-success' : 'text-warning'" 
                                        th:text="${#numbers.formatDecimal(welcomeData.generateSuccessRate, 1, 2)} + '%'">95.5%</h3>
                                    <small class="text-muted">阈值: ≥95%</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-cogs fa-2x" th:class="${welcomeData.generateSuccessRate >= 95} ? 'text-success' : 'text-warning'"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card" th:classappend="${welcomeData.cacheHitRate >= 80} ? 'success' : 'danger'">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title text-muted">缓存命中率</h6>
                                    <h3 th:class="${welcomeData.cacheHitRate >= 80} ? 'text-success' : 'text-danger'" 
                                        th:text="${#numbers.formatDecimal(welcomeData.cacheHitRate, 1, 2)} + '%'">82.3%</h3>
                                    <small class="text-muted">阈值: ≥80%</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-database fa-2x" th:class="${welcomeData.cacheHitRate >= 80} ? 'text-success' : 'text-danger'"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title text-muted">今日生成总数</h6>
                                    <h3 class="text-primary" th:text="${#lists.size(welcomeData.generateSuccessData) > 0 ? #lists.get(#maps.values(welcomeData.generateSuccessData), #lists.size(#maps.values(welcomeData.generateSuccessData)) - 1) : 0}">1,234</h3>
                                    <small class="text-muted">成功生成数量</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-line fa-2x text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title text-muted">今日缓存命中</h6>
                                    <h3 class="text-info" th:text="${#lists.size(welcomeData.cacheHitData) > 0 ? #lists.get(#maps.values(welcomeData.cacheHitData), #lists.size(#maps.values(welcomeData.cacheHitData)) - 1) : 0}">987</h3>
                                    <small class="text-muted">缓存命中次数</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-memory fa-2x text-info"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="row mb-4">
                <!-- 生成成功率趋势图 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>
                                欢迎语生成趋势
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="generateChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 缓存命中率趋势图 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-database me-2"></i>
                                缓存命中趋势
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="cacheChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细数据表格 -->
            <div class="row mb-4">
                <!-- 重试次数详情 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-redo me-2"></i>
                                重试次数详情
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>时间</th>
                                            <th>企业ID</th>
                                            <th>重试次数</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr th:each="detail : ${welcomeData.retryDetails}" th:if="${#lists.size(welcomeData.retryDetails) > 0}">
                                            <td th:text="${#strings.substringBefore(detail, '|')}">2024-01-01 12:00:00</td>
                                            <td th:text="${#strings.substringBefore(#strings.substringAfter(detail, '|'), '|')}">ww5</td>
                                            <td>
                                                <span class="badge" 
                                                      th:classappend="${#numbers.formatInteger(#strings.substringAfter(#strings.substringAfter(detail, '|'), '|'), 1, 'COMMA') <= 2} ? 'bg-success' : 'bg-warning'"
                                                      th:text="${#strings.substringAfter(#strings.substringAfter(detail, '|'), '|')}">1</span>
                                            </td>
                                        </tr>
                                        <tr th:if="${#lists.size(welcomeData.retryDetails) == 0}">
                                            <td colspan="3" class="text-center text-muted">暂无数据</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 发送耗时详情 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>
                                发送耗时详情
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>时间</th>
                                            <th>企业ID</th>
                                            <th>重试次数</th>
                                            <th>耗时(ms)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr th:each="detail : ${welcomeData.durationDetails}" th:if="${#lists.size(welcomeData.durationDetails) > 0}">
                                            <td th:text="${#strings.substringBefore(detail, '|')}">2024-01-01 12:00:00</td>
                                            <td th:text="${#strings.substringBefore(#strings.substringAfter(detail, '|'), '|')}">ww5</td>
                                            <td th:text="${#strings.substringBefore(#strings.substringAfter(#strings.substringAfter(detail, '|'), '|'), '|')}">0</td>
                                            <td>
                                                <span class="badge" 
                                                      th:classappend="${#numbers.formatInteger(#strings.substringAfter(#strings.substringAfter(#strings.substringAfter(detail, '|'), '|'), '|'), 1, 'COMMA') <= 15000} ? 'bg-success' : 'bg-danger'"
                                                      th:text="${#strings.substringAfter(#strings.substringAfter(#strings.substringAfter(detail, '|'), '|'), '|')}">1200</span>
                                            </td>
                                        </tr>
                                        <tr th:if="${#lists.size(welcomeData.durationDetails) == 0}">
                                            <td colspan="4" class="text-center text-muted">暂无数据</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 告警阈值说明 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                告警阈值说明
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="alert alert-success">
                                        <strong>欢迎语生成成功率</strong><br>
                                        正常: ≥95%<br>
                                        告警: &lt;95%
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="alert alert-info">
                                        <strong>Redis缓存命中率</strong><br>
                                        正常: ≥80%<br>
                                        告警: &lt;80%
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="alert alert-warning">
                                        <strong>重试次数分布</strong><br>
                                        正常: 平均≤2次<br>
                                        告警: 平均&gt;2次
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="alert alert-danger">
                                        <strong>发送耗时</strong><br>
                                        正常: P99≤15s<br>
                                        告警: P99&gt;15s
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script th:unless="${error}">
            // 准备图表数据
            const dates = /*[[${#maps.keys(welcomeData.generateSuccessData)}]]*/ [];
            
            // 欢迎语生成趋势图
            const generateSuccessData = /*[[${#maps.values(welcomeData.generateSuccessData)}]]*/ [];
            const generateFailureData = /*[[${#maps.values(welcomeData.generateFailureData)}]]*/ [];
            
            createLineChart('generateChart', dates, [
                {
                    label: '生成成功',
                    data: generateSuccessData,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                },
                {
                    label: '生成失败',
                    data: generateFailureData,
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4
                }
            ]);

            // 缓存命中趋势图
            const cacheHitData = /*[[${#maps.values(welcomeData.cacheHitData)}]]*/ [];
            const cacheMissData = /*[[${#maps.values(welcomeData.cacheMissData)}]]*/ [];
            
            createLineChart('cacheChart', dates, [
                {
                    label: '缓存命中',
                    data: cacheHitData,
                    borderColor: '#17a2b8',
                    backgroundColor: 'rgba(23, 162, 184, 0.1)',
                    tension: 0.4
                },
                {
                    label: '缓存未命中',
                    data: cacheMissData,
                    borderColor: '#ffc107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    tension: 0.4
                }
            ]);

            // 设置页面标题
            document.title = '欢迎语监控 - 企微客服监控系统';
            
            // 30秒后自动刷新
            autoRefresh(30);
        </script>
    </div>
</body>
</html>
