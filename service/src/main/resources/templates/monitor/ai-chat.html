<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:replace="~{monitor/layout :: html}">
<head>
    <title>AI聊天监控 - 企微客服监控系统</title>
</head>
<body>
    <div th:fragment="content">
        <div th:if="${error}" class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span th:text="${error}">错误信息</span>
        </div>

        <div th:unless="${error}">
            <!-- 关键指标卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card metric-card" th:classappend="${aiChatData != null and aiChatData.syncSuccessRate >= 90} ? 'success' : 'warning'">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title text-muted">同步调用成功率</h6>
                                    <h3 th:class="${aiChatData != null and aiChatData.syncSuccessRate >= 90} ? 'text-success' : 'text-warning'"
                                        th:text="${aiChatData != null ? #numbers.formatDecimal(aiChatData.syncSuccessRate, 1, 2) + '%' : 'N/A'}">92.3%</h3>
                                    <small class="text-muted">阈值: ≥90%</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-sync-alt fa-2x" th:class="${aiChatData != null and aiChatData.syncSuccessRate >= 90} ? 'text-success' : 'text-warning'"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card" th:classappend="${aiChatData != null and aiChatData.asyncSuccessRate >= 90} ? 'success' : 'warning'">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title text-muted">异步调用成功率</h6>
                                    <h3 th:class="${aiChatData != null and aiChatData.asyncSuccessRate >= 90} ? 'text-success' : 'text-warning'"
                                        th:text="${aiChatData != null ? #numbers.formatDecimal(aiChatData.asyncSuccessRate, 1, 2) + '%' : 'N/A'}">88.7%</h3>
                                    <small class="text-muted">阈值: ≥90%</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x" th:class="${aiChatData != null and aiChatData.asyncSuccessRate >= 90} ? 'text-success' : 'text-warning'"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card" th:classappend="${aiChatData != null and aiChatData.takeoverSuccessRate >= 70} ? 'success' : 'danger'">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title text-muted">AI承接成功率</h6>
                                    <h3 th:class="${aiChatData != null and aiChatData.takeoverSuccessRate >= 70} ? 'text-success' : 'text-danger'"
                                        th:text="${aiChatData != null ? #numbers.formatDecimal(aiChatData.takeoverSuccessRate, 1, 2) + '%' : 'N/A'}">75.2%</h3>
                                    <small class="text-muted">阈值: ≥70%</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-robot fa-2x" th:class="${aiChatData != null and aiChatData.takeoverSuccessRate >= 70} ? 'text-success' : 'text-danger'"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title text-muted">今日调用总数</h6>
                                    <h3 class="text-primary" th:text="${aiChatData.syncSuccessData != null and !aiChatData.syncSuccessData.isEmpty() ? #aggregates.sum(aiChatData.syncSuccessData.values()) : 0}">2,456</h3>
                                    <small class="text-muted">同步调用成功数</small>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-chart-bar fa-2x text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="row mb-4">
                <!-- 同步调用趋势图 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-sync-alt me-2"></i>
                                同步调用趋势
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="syncChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 异步调用趋势图 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>
                                异步调用趋势
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="asyncChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI结果类型分布 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-pie-chart me-2"></i>
                                AI结果类型分布
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="resultTypeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 响应时间详情 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-stopwatch me-2"></i>
                                响应时间详情
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>时间</th>
                                            <th>企业ID</th>
                                            <th>调用类型</th>
                                            <th>响应时间(ms)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr th:each="detail : ${aiChatData.responseTimeDetails}" th:if="${#lists.size(aiChatData.responseTimeDetails) > 0}">
                                            <td th:text="${#strings.substringBefore(detail, '|')}">2024-01-01 12:00:00</td>
                                            <td th:text="${#strings.substringBefore(#strings.substringAfter(detail, '|'), '|')}">ww5</td>
                                            <td th:text="${#strings.substringBefore(#strings.substringAfter(#strings.substringAfter(detail, '|'), '|'), '|')}">sync</td>
                                            <td>
                                                <span class="badge" 
                                                      th:classappend="${#numbers.formatInteger(#strings.substringAfter(#strings.substringAfter(#strings.substringAfter(detail, '|'), '|'), '|'), 1, 'COMMA') <= 20000} ? 'bg-success' : 'bg-danger'"
                                                      th:text="${#strings.substringAfter(#strings.substringAfter(#strings.substringAfter(detail, '|'), '|'), '|')}">1500</span>
                                            </td>
                                        </tr>
                                        <tr th:if="${#lists.size(aiChatData.responseTimeDetails) == 0}">
                                            <td colspan="4" class="text-center text-muted">暂无数据</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 告警阈值说明 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                告警阈值说明
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="alert alert-success">
                                        <strong>AI调用成功率</strong><br>
                                        正常: ≥90%<br>
                                        告警: &lt;90%
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="alert alert-info">
                                        <strong>AI承接率</strong><br>
                                        正常: ≥70%<br>
                                        告警: &lt;70%
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="alert alert-warning">
                                        <strong>AI响应时间</strong><br>
                                        正常: P99≤20s<br>
                                        告警: P99&gt;20s
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="alert alert-danger">
                                        <strong>转人工率</strong><br>
                                        正常: ≤30%<br>
                                        告警: &gt;30%
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script th:unless="${error}">
            // 准备图表数据
            const dates = /*[[${aiChatData != null and aiChatData.syncSuccessData != null ? #maps.keys(aiChatData.syncSuccessData) : {}}]]*/ [];

            // 同步调用趋势图
            const syncSuccessData = /*[[${aiChatData != null and aiChatData.syncSuccessData != null ? #maps.values(aiChatData.syncSuccessData) : {}}]]*/ [];
            const syncFailureData = /*[[${aiChatData != null and aiChatData.syncFailureData != null ? #maps.values(aiChatData.syncFailureData) : {}}]]*/ [];
            
            createLineChart('syncChart', dates, [
                {
                    label: '调用成功',
                    data: syncSuccessData,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                },
                {
                    label: '调用失败',
                    data: syncFailureData,
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4
                }
            ]);

            // 异步调用趋势图
            const asyncSuccessData = /*[[${aiChatData != null and aiChatData.asyncSuccessData != null ? #maps.values(aiChatData.asyncSuccessData) : {}}]]*/ [];
            const asyncFailureData = /*[[${aiChatData != null and aiChatData.asyncFailureData != null ? #maps.values(aiChatData.asyncFailureData) : {}}]]*/ [];
            
            createLineChart('asyncChart', dates, [
                {
                    label: '调用成功',
                    data: asyncSuccessData,
                    borderColor: '#17a2b8',
                    backgroundColor: 'rgba(23, 162, 184, 0.1)',
                    tension: 0.4
                },
                {
                    label: '调用失败',
                    data: asyncFailureData,
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4
                }
            ]);

            // AI结果类型分布饼图
            const resultTypeData = /*[[${aiChatData != null and aiChatData.resultTypeData != null ? aiChatData.resultTypeData : {}}]]*/ {};
            const typeLabels = ['普通消息', '转人工', '消息丢弃', '用户要求转人工'];
            const typeValues = [
                Object.values(resultTypeData.type_0 || {}).reduce((a, b) => a + b, 0),
                Object.values(resultTypeData.type_1 || {}).reduce((a, b) => a + b, 0),
                Object.values(resultTypeData.type_3 || {}).reduce((a, b) => a + b, 0),
                Object.values(resultTypeData.type_5 || {}).reduce((a, b) => a + b, 0)
            ];
            
            createPieChart('resultTypeChart', typeLabels, typeValues);

            // 设置页面标题
            document.title = 'AI聊天监控 - 企微客服监控系统';
            
            // 30秒后自动刷新
            autoRefresh(30);
        </script>
    </div>
</body>
</html>
