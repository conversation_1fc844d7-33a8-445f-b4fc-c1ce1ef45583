<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:fragment="html">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title} ?: '企微客服监控系统'">企微客服监控系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #495057;
        }
        .metric-card {
            border-left: 4px solid #007bff;
        }
        .metric-card.success {
            border-left-color: #28a745;
        }
        .metric-card.warning {
            border-left-color: #ffc107;
        }
        .metric-card.danger {
            border-left-color: #dc3545;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .table-responsive {
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-2 d-none d-md-block sidebar">
                <div class="sidebar-sticky pt-3">
                    <h5 class="text-white text-center mb-4">监控系统</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#strings.contains(#httpServletRequest.requestURI, '/overview')} ? 'active' : ''" 
                               th:href="@{/monitor/view/overview(corpId=${corpId}, days=${days})}">
                                <i class="fas fa-tachometer-alt"></i> 监控概览
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#strings.contains(#httpServletRequest.requestURI, '/welcome')} ? 'active' : ''" 
                               th:href="@{/monitor/view/welcome(corpId=${corpId}, days=${days})}">
                                <i class="fas fa-comments"></i> 欢迎语监控
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#strings.contains(#httpServletRequest.requestURI, '/ai-chat')} ? 'active' : ''" 
                               th:href="@{/monitor/view/ai-chat(corpId=${corpId}, days=${days})}">
                                <i class="fas fa-robot"></i> AI聊天监控
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#strings.contains(#httpServletRequest.requestURI, '/message-process')} ? 'active' : ''" 
                               th:href="@{/monitor/view/message-process(corpId=${corpId}, days=${days})}">
                                <i class="fas fa-envelope"></i> 消息处理监控
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#strings.contains(#httpServletRequest.requestURI, '/user-satisfaction')} ? 'active' : ''" 
                               th:href="@{/monitor/view/user-satisfaction(corpId=${corpId}, days=${days})}">
                                <i class="fas fa-smile"></i> 用户满意度监控
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:classappend="${#strings.contains(#httpServletRequest.requestURI, '/external-service')} ? 'active' : ''" 
                               th:href="@{/monitor/view/external-service(corpId=${corpId}, days=${days})}">
                                <i class="fas fa-external-link-alt"></i> 外部服务监控
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-10 ml-sm-auto px-4">
                <!-- 顶部工具栏 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2" th:text="${pageTitle} ?: '监控页面'">监控页面</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <form class="d-flex" method="get">
                            <select name="corpId" class="form-select me-2" th:value="${corpId}">
                                <option value="ww5cfa32107e9a1f20" th:selected="${corpId == 'ww5cfa32107e9a1f20'}">精选客服(ww5cfa32107e9a1f20)</option>
                                <option value="default_corp_1" th:selected="${corpId == 'default_corp_1'}">测试企业1</option>
                                <option value="default_corp_2" th:selected="${corpId == 'default_corp_2'}">测试企业2</option>
                            </select>
                            <select name="days" class="form-select me-2" th:value="${days}">
                                <option value="1" th:selected="${days == 1}">最近1天</option>
                                <option value="3" th:selected="${days == 3}">最近3天</option>
                                <option value="7" th:selected="${days == 7}">最近7天</option>
                            </select>
                            <input type="hidden" name="bizScene" th:value="${bizScene}" th:if="${bizScene}">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sync-alt"></i> 刷新
                            </button>
                        </form>
                    </div>
                </div>

                <!-- 页面内容 -->
                <div th:insert="~{::content}">
                    <!-- 子页面内容将在这里显示 -->
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 通用JavaScript函数 -->
    <script>
        // 创建折线图
        function createLineChart(canvasId, labels, datasets) {
            const ctx = document.getElementById(canvasId).getContext('2d');
            return new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 创建柱状图
        function createBarChart(canvasId, labels, datasets) {
            const ctx = document.getElementById(canvasId).getContext('2d');
            return new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 创建饼图
        function createPieChart(canvasId, labels, data) {
            const ctx = document.getElementById(canvasId).getContext('2d');
            return new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF',
                            '#FF9F40'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }

        // 格式化数字
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        // 格式化百分比
        function formatPercentage(num) {
            return num.toFixed(2) + '%';
        }

        // 自动刷新页面
        function autoRefresh(interval) {
            setTimeout(function() {
                location.reload();
            }, interval * 1000);
        }
    </script>
</body>
</html>
