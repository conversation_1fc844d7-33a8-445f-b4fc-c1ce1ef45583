<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监控仪表板 - 企微客服监控系统</title>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #1d1d1f;
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
        }

        .header h1 {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            letter-spacing: -0.02em;
        }

        .header p {
            font-size: 1.25rem;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 400;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 50px;
            flex-wrap: wrap;
        }

        .control-group {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .control-group select {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 16px;
            font-weight: 500;
            color: #1d1d1f;
            margin: 0 10px;
            min-width: 150px;
        }

        .refresh-btn {
            background: linear-gradient(135deg, #007aff, #5856d6);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 122, 255, 0.3);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #007aff, #5856d6);
        }

        .metric-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .metric-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .metric-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 24px;
            color: white;
        }

        .metric-icon.success { background: linear-gradient(135deg, #34c759, #30d158); }
        .metric-icon.warning { background: linear-gradient(135deg, #ff9500, #ff9f0a); }
        .metric-icon.danger { background: linear-gradient(135deg, #ff3b30, #ff453a); }
        .metric-icon.info { background: linear-gradient(135deg, #007aff, #5856d6); }

        .metric-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1d1d1f;
            margin: 0;
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 15px 0;
            color: #1d1d1f;
        }

        .metric-subtitle {
            font-size: 0.9rem;
            color: #86868b;
            font-weight: 500;
        }

        .charts-section {
            margin-top: 60px;
        }

        .section-title {
            font-size: 2rem;
            font-weight: 700;
            color: white;
            text-align: center;
            margin-bottom: 40px;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .chart-title i {
            margin-right: 10px;
            color: #007aff;
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.success { background: #34c759; }
        .status-indicator.warning { background: #ff9500; }
        .status-indicator.danger { background: #ff3b30; }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .charts-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 错误提示 -->
        <div th:if="${error}" style="background: rgba(255, 59, 48, 0.1); color: #ff3b30; padding: 20px; border-radius: 16px; margin-bottom: 30px; text-align: center;">
            <i class="fas fa-exclamation-triangle" style="margin-right: 10px;"></i>
            <span th:text="${error}">错误信息</span>
        </div>

        <div th:unless="${error}">
            <!-- 页面头部 -->
            <div class="header">
                <h1>监控仪表板</h1>
                <p>实时监控企微客服系统运行状态</p>
                <div style="margin-top: 20px;">
                    <span style="display: inline-flex; align-items: center; background: rgba(52, 199, 89, 0.1); color: #34c759; padding: 8px 16px; border-radius: 20px; font-weight: 600;">
                        <span style="width: 8px; height: 8px; background: #34c759; border-radius: 50%; margin-right: 8px; animation: pulse 2s infinite;"></span>
                        系统运行正常
                    </span>
                </div>
            </div>

            <style>
                @keyframes pulse {
                    0% { opacity: 1; }
                    50% { opacity: 0.5; }
                    100% { opacity: 1; }
                }
            </style>

            <!-- 控制面板 -->
            <div class="controls">
                <div class="control-group">
                    <form method="get" style="display: flex; align-items: center; gap: 15px;">
                        <select name="corpId" th:value="${corpId}">
                            <option value="ww5cfa32107e9a1f20" th:selected="${corpId == 'ww5cfa32107e9a1f20'}">精选客服</option>
                            <option value="default_corp_1" th:selected="${corpId == 'default_corp_1'}">测试企业1</option>
                            <option value="default_corp_2" th:selected="${corpId == 'default_corp_2'}">测试企业2</option>
                        </select>
                        <select name="days" th:value="${days}">
                            <option value="1" th:selected="${days == 1}">最近1天</option>
                            <option value="3" th:selected="${days == 3}">最近3天</option>
                            <option value="7" th:selected="${days == 7}">最近7天</option>
                        </select>
                        <button type="submit" class="refresh-btn">
                            <i class="fas fa-sync-alt" style="margin-right: 8px;"></i>
                            刷新数据
                        </button>
                    </form>
                </div>
            </div>

            <!-- 关键指标 -->
            <div class="metrics-grid">
                <!-- 欢迎语生成成功率 -->
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon success">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h3 class="metric-title">欢迎语生成成功率</h3>
                    </div>
                    <div class="metric-value" th:text="${overviewData != null and overviewData.welcome != null ? #numbers.formatDecimal(overviewData.welcome.generateSuccessRate, 1, 2) + '%' : 'N/A'}">95.5%</div>
                    <div class="metric-subtitle">
                        <span class="status-indicator success"></span>
                        系统运行正常
                    </div>
                </div>

                <!-- AI调用成功率 -->
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon" th:classappend="${overviewData != null and overviewData.aiChat != null and overviewData.aiChat.syncSuccessRate >= 90} ? 'success' : 'warning'">
                            <i class="fas fa-robot"></i>
                        </div>
                        <h3 class="metric-title">AI调用成功率</h3>
                    </div>
                    <div class="metric-value" th:text="${overviewData != null and overviewData.aiChat != null ? #numbers.formatDecimal(overviewData.aiChat.syncSuccessRate, 1, 2) + '%' : 'N/A'}">92.3%</div>
                    <div class="metric-subtitle">
                        <span class="status-indicator" th:classappend="${overviewData != null and overviewData.aiChat != null and overviewData.aiChat.syncSuccessRate >= 90} ? 'success' : 'warning'"></span>
                        <span th:text="${overviewData != null and overviewData.aiChat != null and overviewData.aiChat.syncSuccessRate >= 90} ? '运行良好' : '需要关注'">运行良好</span>
                    </div>
                </div>

                <!-- 首次咨询承接率 -->
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon" th:classappend="${overviewData != null and overviewData.messageProcess != null and overviewData.messageProcess.firstConsultSuccessRate >= 85} ? 'success' : 'danger'">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h3 class="metric-title">首次咨询承接率</h3>
                    </div>
                    <div class="metric-value" th:text="${overviewData != null and overviewData.messageProcess != null ? #numbers.formatDecimal(overviewData.messageProcess.firstConsultSuccessRate, 1, 2) + '%' : 'N/A'}">87.8%</div>
                    <div class="metric-subtitle">
                        <span class="status-indicator" th:classappend="${overviewData != null and overviewData.messageProcess != null and overviewData.messageProcess.firstConsultSuccessRate >= 85} ? 'success' : 'danger'"></span>
                        <span th:text="${overviewData != null and overviewData.messageProcess != null and overviewData.messageProcess.firstConsultSuccessRate >= 85} ? '表现优秀' : '需要改进'">表现优秀</span>
                    </div>
                </div>

                <!-- 用户满意度 -->
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon" th:classappend="${overviewData != null and overviewData.userSatisfaction != null and overviewData.userSatisfaction.satisfactionRate >= 60} ? 'success' : 'danger'">
                            <i class="fas fa-smile"></i>
                        </div>
                        <h3 class="metric-title">用户满意度</h3>
                    </div>
                    <div class="metric-value" th:text="${overviewData != null and overviewData.userSatisfaction != null ? #numbers.formatDecimal(overviewData.userSatisfaction.satisfactionRate, 1, 2) + '%' : 'N/A'}">72.1%</div>
                    <div class="metric-subtitle">
                        <span class="status-indicator" th:classappend="${overviewData != null and overviewData.userSatisfaction != null and overviewData.userSatisfaction.satisfactionRate >= 60} ? 'success' : 'danger'"></span>
                        <span th:text="${overviewData != null and overviewData.userSatisfaction != null and overviewData.userSatisfaction.satisfactionRate >= 60} ? '用户满意' : '待提升'">用户满意</span>
                    </div>
                </div>

                <!-- 外部服务调用成功率 -->
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon" th:classappend="${overviewData != null and overviewData.externalService != null and overviewData.externalService.difySuccessRate >= 90} ? 'success' : 'warning'">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                        <h3 class="metric-title">外部服务调用成功率</h3>
                    </div>
                    <div class="metric-value" th:text="${overviewData != null and overviewData.externalService != null ? #numbers.formatDecimal(overviewData.externalService.difySuccessRate, 1, 2) + '%' : 'N/A'}">94.2%</div>
                    <div class="metric-subtitle">
                        <span class="status-indicator" th:classappend="${overviewData != null and overviewData.externalService != null and overviewData.externalService.difySuccessRate >= 90} ? 'success' : 'warning'"></span>
                        <span th:text="${overviewData != null and overviewData.externalService != null and overviewData.externalService.difySuccessRate >= 90} ? '服务稳定' : '需要关注'">服务稳定</span>
                    </div>
                </div>

                <!-- 缓存命中率 -->
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon" th:classappend="${overviewData != null and overviewData.welcome != null and overviewData.welcome.cacheHitRate >= 80} ? 'success' : 'warning'">
                            <i class="fas fa-memory"></i>
                        </div>
                        <h3 class="metric-title">缓存命中率</h3>
                    </div>
                    <div class="metric-value" th:text="${overviewData != null and overviewData.welcome != null ? #numbers.formatDecimal(overviewData.welcome.cacheHitRate, 1, 2) + '%' : 'N/A'}">88.5%</div>
                    <div class="metric-subtitle">
                        <span class="status-indicator" th:classappend="${overviewData != null and overviewData.welcome != null and overviewData.welcome.cacheHitRate >= 80} ? 'success' : 'warning'"></span>
                        <span th:text="${overviewData != null and overviewData.welcome != null and overviewData.welcome.cacheHitRate >= 80} ? '缓存有效' : '需要优化'">缓存有效</span>
                    </div>
                </div>
            </div>

            <!-- 快速操作区域 -->
            <div style="margin: 40px 0;">
                <div style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(20px); border-radius: 24px; padding: 30px; border: 1px solid rgba(255, 255, 255, 0.3);">
                    <h3 style="font-size: 1.5rem; font-weight: 600; color: #1d1d1f; margin-bottom: 20px; text-align: center;">
                        <i class="fas fa-tools" style="margin-right: 10px; color: #007aff;"></i>
                        快速操作
                    </h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <a href="/monitor/view/welcome" style="display: flex; align-items: center; padding: 15px; background: linear-gradient(135deg, #34c759, #30d158); color: white; text-decoration: none; border-radius: 12px; font-weight: 600; transition: transform 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                            <i class="fas fa-comments" style="margin-right: 10px;"></i>
                            欢迎语详情
                        </a>
                        <a href="/monitor/view/ai-chat" style="display: flex; align-items: center; padding: 15px; background: linear-gradient(135deg, #007aff, #5856d6); color: white; text-decoration: none; border-radius: 12px; font-weight: 600; transition: transform 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                            <i class="fas fa-robot" style="margin-right: 10px;"></i>
                            AI聊天详情
                        </a>
                        <a href="/monitor/view/message-process" style="display: flex; align-items: center; padding: 15px; background: linear-gradient(135deg, #ff9500, #ff9f0a); color: white; text-decoration: none; border-radius: 12px; font-weight: 600; transition: transform 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                            <i class="fas fa-envelope" style="margin-right: 10px;"></i>
                            消息处理详情
                        </a>
                        <a href="/monitor/view/user-satisfaction" style="display: flex; align-items: center; padding: 15px; background: linear-gradient(135deg, #ff2d92, #ff375f); color: white; text-decoration: none; border-radius: 12px; font-weight: 600; transition: transform 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                            <i class="fas fa-smile" style="margin-right: 10px;"></i>
                            满意度详情
                        </a>
                        <a href="/monitor/view/external-service" style="display: flex; align-items: center; padding: 15px; background: linear-gradient(135deg, #5ac8fa, #007aff); color: white; text-decoration: none; border-radius: 12px; font-weight: 600; transition: transform 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                            <i class="fas fa-external-link-alt" style="margin-right: 10px;"></i>
                            外部服务详情
                        </a>
                        <button onclick="location.reload()" style="display: flex; align-items: center; padding: 15px; background: linear-gradient(135deg, #8e8e93, #636366); color: white; border: none; border-radius: 12px; font-weight: 600; cursor: pointer; transition: transform 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                            <i class="fas fa-sync-alt" style="margin-right: 10px;"></i>
                            刷新数据
                        </button>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="charts-section">
                <h2 class="section-title">数据趋势分析</h2>
                <div class="charts-grid">
                    <!-- 欢迎语趋势图 -->
                    <div class="chart-card">
                        <h3 class="chart-title">
                            <i class="fas fa-comments"></i>
                            欢迎语生成趋势
                        </h3>
                        <div class="chart-container">
                            <canvas id="welcomeChart"></canvas>
                        </div>
                    </div>

                    <!-- AI调用趋势图 -->
                    <div class="chart-card">
                        <h3 class="chart-title">
                            <i class="fas fa-robot"></i>
                            AI调用趋势
                        </h3>
                        <div class="chart-container">
                            <canvas id="aiChart"></canvas>
                        </div>
                    </div>

                    <!-- 消息处理趋势图 -->
                    <div class="chart-card">
                        <h3 class="chart-title">
                            <i class="fas fa-envelope"></i>
                            消息处理趋势
                        </h3>
                        <div class="chart-container">
                            <canvas id="messageChart"></canvas>
                        </div>
                    </div>

                    <!-- 外部服务调用趋势图 -->
                    <div class="chart-card">
                        <h3 class="chart-title">
                            <i class="fas fa-external-link-alt"></i>
                            外部服务调用趋势
                        </h3>
                        <div class="chart-container">
                            <canvas id="externalChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script th:unless="${error}">
        // 准备图表数据
        const dates = /*[[${overviewData != null and overviewData.welcome != null and overviewData.welcome.generateSuccessData != null ? #maps.keys(overviewData.welcome.generateSuccessData) : {}}]]*/ [];

        // 苹果风格的颜色配置
        const appleColors = {
            blue: '#007aff',
            green: '#34c759',
            orange: '#ff9500',
            red: '#ff3b30',
            purple: '#5856d6',
            teal: '#5ac8fa',
            pink: '#ff2d92',
            yellow: '#ffcc00'
        };

        // 创建现代化的图表配置
        function createModernChart(canvasId, type, labels, datasets) {
            const ctx = document.getElementById(canvasId).getContext('2d');
            return new Chart(ctx, {
                type: type,
                data: {
                    labels: labels,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20,
                                font: {
                                    family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
                                    size: 12,
                                    weight: '500'
                                }
                            }
                        }
                    },
                    scales: type === 'line' ? {
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif'
                                }
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                font: {
                                    family: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif'
                                }
                            }
                        }
                    } : {}
                }
            });
        }

        // 欢迎语趋势图
        const welcomeSuccessData = /*[[${overviewData != null and overviewData.welcome != null and overviewData.welcome.generateSuccessData != null ? #maps.values(overviewData.welcome.generateSuccessData) : {}}]]*/ [];
        const welcomeFailureData = /*[[${overviewData != null and overviewData.welcome != null and overviewData.welcome.generateFailureData != null ? #maps.values(overviewData.welcome.generateFailureData) : {}}]]*/ [];

        createModernChart('welcomeChart', 'line', dates, [
            {
                label: '生成成功',
                data: welcomeSuccessData,
                borderColor: appleColors.green,
                backgroundColor: appleColors.green + '20',
                borderWidth: 3,
                tension: 0.4,
                pointBackgroundColor: appleColors.green,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            },
            {
                label: '生成失败',
                data: welcomeFailureData,
                borderColor: appleColors.red,
                backgroundColor: appleColors.red + '20',
                borderWidth: 3,
                tension: 0.4,
                pointBackgroundColor: appleColors.red,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }
        ]);

        // AI调用趋势图
        const aiSuccessData = /*[[${overviewData != null and overviewData.aiChat != null and overviewData.aiChat.syncSuccessData != null ? #maps.values(overviewData.aiChat.syncSuccessData) : {}}]]*/ [];
        const aiFailureData = /*[[${overviewData != null and overviewData.aiChat != null and overviewData.aiChat.syncFailureData != null ? #maps.values(overviewData.aiChat.syncFailureData) : {}}]]*/ [];

        createModernChart('aiChart', 'line', dates, [
            {
                label: 'AI调用成功',
                data: aiSuccessData,
                borderColor: appleColors.blue,
                backgroundColor: appleColors.blue + '20',
                borderWidth: 3,
                tension: 0.4,
                pointBackgroundColor: appleColors.blue,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            },
            {
                label: 'AI调用失败',
                data: aiFailureData,
                borderColor: appleColors.red,
                backgroundColor: appleColors.red + '20',
                borderWidth: 3,
                tension: 0.4,
                pointBackgroundColor: appleColors.red,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }
        ]);

        // 消息处理趋势图
        const userMessageData = /*[[${overviewData != null and overviewData.messageProcess != null and overviewData.messageProcess.userMessageData != null ? #maps.values(overviewData.messageProcess.userMessageData) : {}}]]*/ [];
        const botMessageData = /*[[${overviewData != null and overviewData.messageProcess != null and overviewData.messageProcess.botMessageData != null ? #maps.values(overviewData.messageProcess.botMessageData) : {}}]]*/ [];

        createModernChart('messageChart', 'line', dates, [
            {
                label: '用户消息',
                data: userMessageData,
                borderColor: appleColors.orange,
                backgroundColor: appleColors.orange + '20',
                borderWidth: 3,
                tension: 0.4,
                pointBackgroundColor: appleColors.orange,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            },
            {
                label: '机器人消息',
                data: botMessageData,
                borderColor: appleColors.purple,
                backgroundColor: appleColors.purple + '20',
                borderWidth: 3,
                tension: 0.4,
                pointBackgroundColor: appleColors.purple,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }
        ]);

        // 外部服务调用趋势图
        const difySuccessData = /*[[${overviewData != null and overviewData.externalService != null and overviewData.externalService.difySuccessData != null ? #maps.values(overviewData.externalService.difySuccessData) : {}}]]*/ [];
        const difyFailureData = /*[[${overviewData != null and overviewData.externalService != null and overviewData.externalService.difyFailureData != null ? #maps.values(overviewData.externalService.difyFailureData) : {}}]]*/ [];

        createModernChart('externalChart', 'line', dates, [
            {
                label: 'Dify调用成功',
                data: difySuccessData,
                borderColor: appleColors.teal,
                backgroundColor: appleColors.teal + '20',
                borderWidth: 3,
                tension: 0.4,
                pointBackgroundColor: appleColors.teal,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            },
            {
                label: 'Dify调用失败',
                data: difyFailureData,
                borderColor: appleColors.red,
                backgroundColor: appleColors.red + '20',
                borderWidth: 3,
                tension: 0.4,
                pointBackgroundColor: appleColors.red,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }
        ]);

        // 设置页面标题
        document.title = '监控仪表板 - 企微客服监控系统';

        // 30秒后自动刷新
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
