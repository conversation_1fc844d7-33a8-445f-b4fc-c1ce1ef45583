<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精选客服监控仪表板</title>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', Helvetica, Arial, sans-serif;
            background: #f5f5f7;
            min-height: 100vh;
            color: #1d1d1f;
            line-height: 1.47059;
            font-weight: 400;
            letter-spacing: -0.022em;
        }

        .dashboard-container {
            max-width: 1440px;
            margin: 0 auto;
            padding: 0;
        }

        .hero-section {
            background: linear-gradient(135deg, #000000 0%, #1c1c1e 100%);
            padding: 80px 40px 60px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(ellipse at center, rgba(0, 122, 255, 0.1) 0%, transparent 70%);
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .hero-title {
            font-size: 56px;
            font-weight: 600;
            color: #f5f5f7;
            margin-bottom: 16px;
            letter-spacing: -0.005em;
            line-height: 1.07143;
        }

        .hero-subtitle {
            font-size: 28px;
            font-weight: 400;
            color: #a1a1a6;
            margin-bottom: 40px;
            letter-spacing: 0.007em;
            line-height: 1.14286;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            background: rgba(52, 199, 89, 0.1);
            border: 1px solid rgba(52, 199, 89, 0.3);
            color: #30d158;
            padding: 12px 24px;
            border-radius: 50px;
            font-weight: 500;
            font-size: 17px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #30d158;
            border-radius: 50%;
            margin-right: 12px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .controls-section {
            background: #fbfbfd;
            padding: 40px;
            border-bottom: 1px solid #d2d2d7;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 24px;
            flex-wrap: wrap;
            max-width: 800px;
            margin: 0 auto;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .control-group select {
            background: #ffffff;
            border: 1px solid #d2d2d7;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 17px;
            font-weight: 400;
            color: #1d1d1f;
            min-width: 180px;
            transition: all 0.2s ease;
        }

        .control-group select:focus {
            outline: none;
            border-color: #007aff;
            box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
        }

        .refresh-btn {
            background: #007aff;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-size: 17px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .refresh-btn:hover {
            background: #0056cc;
            transform: translateY(-1px);
        }

        .content-section {
            background: #f5f5f7;
            padding: 80px 40px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
            margin-bottom: 80px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .metric-card {
            background: #ffffff;
            border-radius: 18px;
            padding: 32px;
            border: 1px solid #e5e5e7;
            transition: all 0.3s ease;
            position: relative;
        }

        .metric-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08);
        }

        .metric-header {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
        }

        .metric-icon {
            width: 44px;
            height: 44px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 20px;
            color: white;
        }

        .metric-icon.success { background: #34c759; }
        .metric-icon.warning { background: #ff9500; }
        .metric-icon.danger { background: #ff3b30; }
        .metric-icon.info { background: #007aff; }

        .metric-title {
            font-size: 19px;
            font-weight: 600;
            color: #1d1d1f;
            margin: 0;
            letter-spacing: 0.012em;
        }

        .metric-value {
            font-size: 48px;
            font-weight: 600;
            margin: 16px 0;
            color: #1d1d1f;
            letter-spacing: -0.003em;
            line-height: 1.08349;
        }

        .metric-subtitle {
            font-size: 17px;
            color: #86868b;
            font-weight: 400;
            display: flex;
            align-items: center;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.success { background: #34c759; }
        .status-indicator.warning { background: #ff9500; }
        .status-indicator.danger { background: #ff3b30; }

        .charts-section {
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            font-size: 40px;
            font-weight: 600;
            color: #1d1d1f;
            text-align: center;
            margin-bottom: 48px;
            letter-spacing: -0.003em;
            line-height: 1.1;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(560px, 1fr));
            gap: 24px;
            margin-bottom: 80px;
        }

        .chart-card {
            background: #ffffff;
            border-radius: 18px;
            padding: 32px;
            border: 1px solid #e5e5e7;
            transition: all 0.3s ease;
        }

        .chart-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.06);
        }

        .chart-title {
            font-size: 24px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            letter-spacing: 0.009em;
        }

        .chart-title i {
            margin-right: 12px;
            color: #007aff;
            font-size: 20px;
        }

        .chart-container {
            position: relative;
            height: 320px;
        }

        .data-management-section {
            background: #ffffff;
            border-radius: 18px;
            padding: 40px;
            border: 1px solid #e5e5e7;
            margin-top: 40px;
        }

        .management-title {
            font-size: 28px;
            font-weight: 600;
            color: #1d1d1f;
            text-align: center;
            margin-bottom: 32px;
            letter-spacing: 0.007em;
        }

        .management-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            max-width: 600px;
            margin: 0 auto;
        }

        .management-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 16px 24px;
            border: none;
            border-radius: 12px;
            font-size: 17px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            gap: 8px;
        }

        .management-btn.primary {
            background: #34c759;
            color: white;
        }

        .management-btn.primary:hover {
            background: #28a745;
            transform: translateY(-1px);
        }

        .management-btn.danger {
            background: #ff3b30;
            color: white;
        }

        .management-btn.danger:hover {
            background: #d70015;
            transform: translateY(-1px);
        }

        .management-btn.secondary {
            background: #007aff;
            color: white;
        }

        .management-btn.secondary:hover {
            background: #0056cc;
            transform: translateY(-1px);
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 40px;
            }

            .hero-subtitle {
                font-size: 21px;
            }

            .controls {
                flex-direction: column;
                align-items: center;
            }

            .charts-grid {
                grid-template-columns: 1fr;
            }

            .content-section {
                padding: 40px 20px;
            }

            .hero-section {
                padding: 60px 20px 40px;
            }
        }
    </style>
</head>
<body>
    <!-- 错误提示 -->
    <div th:if="${error}" style="background: #ff3b30; color: white; padding: 20px; text-align: center;">
        <i class="fas fa-exclamation-triangle" style="margin-right: 10px;"></i>
        <span th:text="${error}">错误信息</span>
    </div>

    <div th:unless="${error}">
        <!-- Hero Section -->
        <div class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">精选客服监控仪表板</h1>
                <p class="hero-subtitle">实时监控精选客服系统运行状态</p>
                <div class="status-badge">
                    <span class="status-dot"></span>
                    系统运行正常
                </div>
            </div>
        </div>

        <!-- Controls Section -->
        <div class="controls-section">
            <div class="controls">
                <form method="get" style="display: flex; align-items: center; gap: 24px; flex-wrap: wrap;">
                    <input type="hidden" name="corpId" value="ww5cfa32107e9a1f20">
                    <div class="control-group">
                        <label style="font-size: 17px; font-weight: 500; color: #1d1d1f;">时间范围：</label>
                        <select name="days" th:value="${days}">
                            <option value="1" th:selected="${days == 1}">最近1天</option>
                            <option value="3" th:selected="${days == 3}">最近3天</option>
                            <option value="7" th:selected="${days == 7}">最近7天</option>
                        </select>
                    </div>
                    <button type="submit" class="refresh-btn">
                        <i class="fas fa-sync-alt"></i>
                        刷新数据
                    </button>
                </form>
            </div>
        </div>

        <!-- Content Section -->
        <div class="content-section">

            <!-- 关键指标 -->
            <div class="metrics-grid">
                <!-- 欢迎语生成成功率 -->
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon success">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h3 class="metric-title">欢迎语生成成功率</h3>
                    </div>
                    <div class="metric-value" th:text="${overviewData != null and overviewData.welcome != null ? #numbers.formatDecimal(overviewData.welcome.generateSuccessRate, 1, 2) + '%' : 'N/A'}">95.5%</div>
                    <div class="metric-subtitle">
                        <span class="status-indicator success"></span>
                        系统运行正常
                    </div>
                </div>

                <!-- AI调用成功率 -->
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon" th:classappend="${overviewData != null and overviewData.aiChat != null and overviewData.aiChat.syncSuccessRate >= 90} ? 'success' : 'warning'">
                            <i class="fas fa-robot"></i>
                        </div>
                        <h3 class="metric-title">AI调用成功率</h3>
                    </div>
                    <div class="metric-value" th:text="${overviewData != null and overviewData.aiChat != null ? #numbers.formatDecimal(overviewData.aiChat.syncSuccessRate, 1, 2) + '%' : 'N/A'}">92.3%</div>
                    <div class="metric-subtitle">
                        <span class="status-indicator" th:classappend="${overviewData != null and overviewData.aiChat != null and overviewData.aiChat.syncSuccessRate >= 90} ? 'success' : 'warning'"></span>
                        <span th:text="${overviewData != null and overviewData.aiChat != null and overviewData.aiChat.syncSuccessRate >= 90} ? '运行良好' : '需要关注'">运行良好</span>
                    </div>
                </div>

                <!-- 首次咨询承接率 -->
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon" th:classappend="${overviewData != null and overviewData.messageProcess != null and overviewData.messageProcess.firstConsultSuccessRate >= 85} ? 'success' : 'danger'">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h3 class="metric-title">首次咨询承接率</h3>
                    </div>
                    <div class="metric-value" th:text="${overviewData != null and overviewData.messageProcess != null ? #numbers.formatDecimal(overviewData.messageProcess.firstConsultSuccessRate, 1, 2) + '%' : 'N/A'}">87.8%</div>
                    <div class="metric-subtitle">
                        <span class="status-indicator" th:classappend="${overviewData != null and overviewData.messageProcess != null and overviewData.messageProcess.firstConsultSuccessRate >= 85} ? 'success' : 'danger'"></span>
                        <span th:text="${overviewData != null and overviewData.messageProcess != null and overviewData.messageProcess.firstConsultSuccessRate >= 85} ? '表现优秀' : '需要改进'">表现优秀</span>
                    </div>
                </div>

                <!-- 用户满意度 -->
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon" th:classappend="${overviewData != null and overviewData.userSatisfaction != null and overviewData.userSatisfaction.satisfactionRate >= 60} ? 'success' : 'danger'">
                            <i class="fas fa-smile"></i>
                        </div>
                        <h3 class="metric-title">用户满意度</h3>
                    </div>
                    <div class="metric-value" th:text="${overviewData != null and overviewData.userSatisfaction != null ? #numbers.formatDecimal(overviewData.userSatisfaction.satisfactionRate, 1, 2) + '%' : 'N/A'}">72.1%</div>
                    <div class="metric-subtitle">
                        <span class="status-indicator" th:classappend="${overviewData != null and overviewData.userSatisfaction != null and overviewData.userSatisfaction.satisfactionRate >= 60} ? 'success' : 'danger'"></span>
                        <span th:text="${overviewData != null and overviewData.userSatisfaction != null and overviewData.userSatisfaction.satisfactionRate >= 60} ? '用户满意' : '待提升'">用户满意</span>
                    </div>
                </div>

                <!-- 外部服务调用成功率 -->
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon" th:classappend="${overviewData != null and overviewData.externalService != null and overviewData.externalService.difySuccessRate >= 90} ? 'success' : 'warning'">
                            <i class="fas fa-external-link-alt"></i>
                        </div>
                        <h3 class="metric-title">外部服务调用成功率</h3>
                    </div>
                    <div class="metric-value" th:text="${overviewData != null and overviewData.externalService != null ? #numbers.formatDecimal(overviewData.externalService.difySuccessRate, 1, 2) + '%' : 'N/A'}">94.2%</div>
                    <div class="metric-subtitle">
                        <span class="status-indicator" th:classappend="${overviewData != null and overviewData.externalService != null and overviewData.externalService.difySuccessRate >= 90} ? 'success' : 'warning'"></span>
                        <span th:text="${overviewData != null and overviewData.externalService != null and overviewData.externalService.difySuccessRate >= 90} ? '服务稳定' : '需要关注'">服务稳定</span>
                    </div>
                </div>

                <!-- 缓存命中率 -->
                <div class="metric-card">
                    <div class="metric-header">
                        <div class="metric-icon" th:classappend="${overviewData != null and overviewData.welcome != null and overviewData.welcome.cacheHitRate >= 80} ? 'success' : 'warning'">
                            <i class="fas fa-memory"></i>
                        </div>
                        <h3 class="metric-title">缓存命中率</h3>
                    </div>
                    <div class="metric-value" th:text="${overviewData != null and overviewData.welcome != null ? #numbers.formatDecimal(overviewData.welcome.cacheHitRate, 1, 2) + '%' : 'N/A'}">88.5%</div>
                    <div class="metric-subtitle">
                        <span class="status-indicator" th:classappend="${overviewData != null and overviewData.welcome != null and overviewData.welcome.cacheHitRate >= 80} ? 'success' : 'warning'"></span>
                        <span th:text="${overviewData != null and overviewData.welcome != null and overviewData.welcome.cacheHitRate >= 80} ? '缓存有效' : '需要优化'">缓存有效</span>
                    </div>
                </div>
            </div>



            <!-- 图表区域 -->
            <div class="charts-section">
                <h2 class="section-title">数据趋势分析</h2>
                <div class="charts-grid">
                    <!-- 欢迎语趋势图 -->
                    <div class="chart-card">
                        <h3 class="chart-title">
                            <i class="fas fa-comments"></i>
                            欢迎语生成趋势
                        </h3>
                        <div class="chart-container">
                            <canvas id="welcomeChart"></canvas>
                        </div>
                    </div>

                    <!-- AI调用趋势图 -->
                    <div class="chart-card">
                        <h3 class="chart-title">
                            <i class="fas fa-robot"></i>
                            AI调用趋势
                        </h3>
                        <div class="chart-container">
                            <canvas id="aiChart"></canvas>
                        </div>
                    </div>

                    <!-- 消息处理趋势图 -->
                    <div class="chart-card">
                        <h3 class="chart-title">
                            <i class="fas fa-envelope"></i>
                            消息处理趋势
                        </h3>
                        <div class="chart-container">
                            <canvas id="messageChart"></canvas>
                        </div>
                    </div>

                    <!-- 外部服务调用趋势图 -->
                    <div class="chart-card">
                        <h3 class="chart-title">
                            <i class="fas fa-external-link-alt"></i>
                            外部服务调用趋势
                        </h3>
                        <div class="chart-container">
                            <canvas id="externalChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 数据管理区域 -->
                <div class="data-management-section">
                    <h2 class="management-title">数据管理</h2>
                    <div id="no-data-notice" style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 12px; padding: 20px; margin-bottom: 24px; text-align: center; display: none;">
                        <i class="fas fa-info-circle" style="color: #856404; margin-right: 8px;"></i>
                        <span style="color: #856404; font-size: 16px;">当前显示的是模拟数据，点击"生成测试数据"获取真实监控数据</span>
                    </div>
                    <div class="management-buttons">
                        <button onclick="generateTestData()" class="management-btn primary">
                            <i class="fas fa-database"></i>
                            生成测试数据
                        </button>
                        <button onclick="clearAllData()" class="management-btn danger">
                            <i class="fas fa-trash-alt"></i>
                            清空数据
                        </button>
                        <button onclick="location.reload()" class="management-btn secondary">
                            <i class="fas fa-sync-alt"></i>
                            刷新数据
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 全局JavaScript函数 -->
    <script>
        // 数据管理函数 - 全局定义，确保按钮可以调用
        function generateTestData() {
            if (confirm('确定要生成测试数据吗？这将需要一些时间。')) {
                showLoading('正在生成测试数据...');
                fetch('/monitor/data/generate?days=7', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.success) {
                        alert('测试数据生成成功！');
                        location.reload();
                    } else {
                        alert('生成失败：' + data.message);
                    }
                })
                .catch(error => {
                    hideLoading();
                    alert('生成失败：' + error.message);
                });
            }
        }

        function clearAllData() {
            if (confirm('确定要清空所有监控数据吗？此操作不可恢复！')) {
                showLoading('正在清空数据...');
                fetch('/monitor/data/clear', {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    if (data.success) {
                        alert('数据清空成功！');
                        location.reload();
                    } else {
                        alert('清空失败：' + data.message);
                    }
                })
                .catch(error => {
                    hideLoading();
                    alert('清空失败：' + error.message);
                });
            }
        }

        function showLoading(message) {
            const loading = document.createElement('div');
            loading.id = 'loading-overlay';
            loading.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                color: white;
                font-size: 18px;
                font-weight: 600;
            `;
            loading.innerHTML = `
                <div style="text-align: center;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 10px;"></i>
                    <div>${message}</div>
                </div>
            `;
            document.body.appendChild(loading);
        }

        function hideLoading() {
            const loading = document.getElementById('loading-overlay');
            if (loading) {
                loading.remove();
            }
        }
    </script>

    <script th:unless="${error}">
        // 调试信息
        console.log('监控数据:', /*[[${overviewData}]]*/ {});

        // 准备图表数据 - 添加默认数据处理
        let rawDates = /*[[${overviewData != null and overviewData.welcome != null and overviewData.welcome.generateSuccessData != null ? #maps.keys(overviewData.welcome.generateSuccessData) : {}}]]*/ [];

        // 如果没有数据，生成默认的日期数组
        let dates = [];
        if (!rawDates || rawDates.length === 0) {
            const today = new Date();
            for (let i = 6; i >= 0; i--) {
                const date = new Date(today);
                date.setDate(date.getDate() - i);
                dates.push(date.toISOString().split('T')[0]);
            }
            console.log('使用默认日期:', dates);
        } else {
            dates = rawDates;
            console.log('使用实际日期:', dates);
        }

        // 苹果官网风格的颜色配置
        const appleColors = {
            blue: '#007aff',
            green: '#34c759',
            orange: '#ff9500',
            red: '#ff3b30',
            purple: '#5856d6',
            teal: '#5ac8fa',
            pink: '#ff2d92',
            yellow: '#ffcc00',
            gray: '#8e8e93'
        };

        // 数据处理函数
        let usingMockData = false;

        function processChartData(dataMap, defaultValue = 0) {
            if (!dataMap || Object.keys(dataMap).length === 0) {
                // 如果没有数据，返回默认数据
                usingMockData = true;
                return dates.map(() => Math.floor(Math.random() * 100) + defaultValue);
            }

            // 检查是否所有值都为0
            const hasRealData = Object.values(dataMap).some(value => value > 0);
            if (!hasRealData) {
                usingMockData = true;
                return dates.map(() => Math.floor(Math.random() * 100) + defaultValue);
            }

            // 按日期顺序返回数据
            return dates.map(date => dataMap[date] || 0);
        }

        // 创建苹果风格的图表配置
        function createModernChart(canvasId, type, labels, datasets) {
            const ctx = document.getElementById(canvasId).getContext('2d');
            return new Chart(ctx, {
                type: type,
                data: {
                    labels: labels,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            align: 'start',
                            labels: {
                                usePointStyle: true,
                                pointStyle: 'circle',
                                padding: 24,
                                font: {
                                    family: '-apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", Helvetica, Arial, sans-serif',
                                    size: 15,
                                    weight: '500'
                                },
                                color: '#1d1d1f'
                            }
                        }
                    },
                    scales: type === 'line' ? {
                        x: {
                            grid: {
                                display: false,
                                drawBorder: false
                            },
                            ticks: {
                                font: {
                                    family: '-apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", Helvetica, Arial, sans-serif',
                                    size: 13
                                },
                                color: '#86868b',
                                padding: 12
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: '#f2f2f7',
                                drawBorder: false
                            },
                            ticks: {
                                font: {
                                    family: '-apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", Helvetica, Arial, sans-serif',
                                    size: 13
                                },
                                color: '#86868b',
                                padding: 12
                            }
                        }
                    } : {}
                }
            });
        }

        // 欢迎语趋势图
        const welcomeSuccessDataRaw = /*[[${overviewData != null and overviewData.welcome != null and overviewData.welcome.generateSuccessData != null ? overviewData.welcome.generateSuccessData : {}}]]*/ {};
        const welcomeFailureDataRaw = /*[[${overviewData != null and overviewData.welcome != null and overviewData.welcome.generateFailureData != null ? overviewData.welcome.generateFailureData : {}}]]*/ {};

        const welcomeSuccessData = processChartData(welcomeSuccessDataRaw, 80);
        const welcomeFailureData = processChartData(welcomeFailureDataRaw, 10);

        console.log('欢迎语数据:', { welcomeSuccessData, welcomeFailureData });

        createModernChart('welcomeChart', 'line', dates, [
            {
                label: '生成成功',
                data: welcomeSuccessData,
                borderColor: appleColors.green,
                backgroundColor: appleColors.green + '15',
                borderWidth: 2.5,
                tension: 0.4,
                pointBackgroundColor: appleColors.green,
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 5,
                pointHoverRadius: 7,
                fill: true
            },
            {
                label: '生成失败',
                data: welcomeFailureData,
                borderColor: appleColors.red,
                backgroundColor: appleColors.red + '15',
                borderWidth: 2.5,
                tension: 0.4,
                pointBackgroundColor: appleColors.red,
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 5,
                pointHoverRadius: 7,
                fill: true
            }
        ]);

        // AI调用趋势图
        const aiSuccessDataRaw = /*[[${overviewData != null and overviewData.aiChat != null and overviewData.aiChat.syncSuccessData != null ? overviewData.aiChat.syncSuccessData : {}}]]*/ {};
        const aiFailureDataRaw = /*[[${overviewData != null and overviewData.aiChat != null and overviewData.aiChat.syncFailureData != null ? overviewData.aiChat.syncFailureData : {}}]]*/ {};

        const aiSuccessData = processChartData(aiSuccessDataRaw, 120);
        const aiFailureData = processChartData(aiFailureDataRaw, 15);

        console.log('AI调用数据:', { aiSuccessData, aiFailureData });

        createModernChart('aiChart', 'line', dates, [
            {
                label: 'AI调用成功',
                data: aiSuccessData,
                borderColor: appleColors.blue,
                backgroundColor: appleColors.blue + '15',
                borderWidth: 2.5,
                tension: 0.4,
                pointBackgroundColor: appleColors.blue,
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 5,
                pointHoverRadius: 7,
                fill: true
            },
            {
                label: 'AI调用失败',
                data: aiFailureData,
                borderColor: appleColors.red,
                backgroundColor: appleColors.red + '15',
                borderWidth: 2.5,
                tension: 0.4,
                pointBackgroundColor: appleColors.red,
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 5,
                pointHoverRadius: 7,
                fill: true
            }
        ]);

        // 消息处理趋势图
        const userMessageDataRaw = /*[[${overviewData != null and overviewData.messageProcess != null and overviewData.messageProcess.userMessageData != null ? overviewData.messageProcess.userMessageData : {}}]]*/ {};
        const botMessageDataRaw = /*[[${overviewData != null and overviewData.messageProcess != null and overviewData.messageProcess.botMessageData != null ? overviewData.messageProcess.botMessageData : {}}]]*/ {};

        const userMessageData = processChartData(userMessageDataRaw, 200);
        const botMessageData = processChartData(botMessageDataRaw, 180);

        console.log('消息处理数据:', { userMessageData, botMessageData });

        createModernChart('messageChart', 'line', dates, [
            {
                label: '用户消息',
                data: userMessageData,
                borderColor: appleColors.orange,
                backgroundColor: appleColors.orange + '15',
                borderWidth: 2.5,
                tension: 0.4,
                pointBackgroundColor: appleColors.orange,
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 5,
                pointHoverRadius: 7,
                fill: true
            },
            {
                label: '机器人消息',
                data: botMessageData,
                borderColor: appleColors.purple,
                backgroundColor: appleColors.purple + '15',
                borderWidth: 2.5,
                tension: 0.4,
                pointBackgroundColor: appleColors.purple,
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 5,
                pointHoverRadius: 7,
                fill: true
            }
        ]);

        // 外部服务调用趋势图
        const difySuccessDataRaw = /*[[${overviewData != null and overviewData.externalService != null and overviewData.externalService.difySuccessData != null ? overviewData.externalService.difySuccessData : {}}]]*/ {};
        const difyFailureDataRaw = /*[[${overviewData != null and overviewData.externalService != null and overviewData.externalService.difyFailureData != null ? overviewData.externalService.difyFailureData : {}}]]*/ {};

        const difySuccessData = processChartData(difySuccessDataRaw, 150);
        const difyFailureData = processChartData(difyFailureDataRaw, 8);

        console.log('外部服务数据:', { difySuccessData, difyFailureData });

        createModernChart('externalChart', 'line', dates, [
            {
                label: 'Dify调用成功',
                data: difySuccessData,
                borderColor: appleColors.teal,
                backgroundColor: appleColors.teal + '15',
                borderWidth: 2.5,
                tension: 0.4,
                pointBackgroundColor: appleColors.teal,
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 5,
                pointHoverRadius: 7,
                fill: true
            },
            {
                label: 'Dify调用失败',
                data: difyFailureData,
                borderColor: appleColors.red,
                backgroundColor: appleColors.red + '15',
                borderWidth: 2.5,
                tension: 0.4,
                pointBackgroundColor: appleColors.red,
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 5,
                pointHoverRadius: 7,
                fill: true
            }
        ]);

        // 设置页面标题
        document.title = '精选客服监控仪表板';

        // 如果使用了模拟数据，显示提示
        if (usingMockData) {
            const notice = document.getElementById('no-data-notice');
            if (notice) {
                notice.style.display = 'block';
            }
        }

        // 30秒后自动刷新
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
