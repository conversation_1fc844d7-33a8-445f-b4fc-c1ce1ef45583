# AI聊天监控页面空白问题修复

## 问题分析

AI聊天监控页面 `https://hyqywxbiz.58.com/monitor/view/ai-chat?corpId=ww5cfa32107e9a1f20&days=3` 显示空白的原因：

1. **企业ID不匹配**：
   - 数据生成器使用：`ww5cfa32107e9a1f20`
   - 页面下拉框选项：`ww5`、`default_corp_1`、`default_corp_2`
   - URL访问使用：`ww5cfa32107e9a1f20`

2. **数据缺失**：
   - Redis中可能没有为正确的企业ID生成测试数据
   - 页面查询的数据key与实际存储的key不匹配

## 修复内容

### 1. 修复页面模板企业ID选项

**文件**: `service/src/main/resources/templates/monitor/layout.html`

```html
<!-- 修改前 -->
<option value="ww5" th:selected="${corpId == 'ww5'}">精选客服(ww5)</option>

<!-- 修改后 -->
<option value="ww5cfa32107e9a1f20" th:selected="${corpId == 'ww5cfa32107e9a1f20'}">精选客服(ww5cfa32107e9a1f20)</option>
```

### 2. 增强数据生成器支持多企业ID

**文件**: `service/src/main/java/com/bj58/hy/wx/qywxbiz/service/common/monitor/MonitorDataGenerator.java`

**主要修改**：
1. 添加企业ID数组常量：
   ```java
   private static final String[] ALL_CORP_IDS = {"ww5cfa32107e9a1f20", "default_corp_1", "default_corp_2"};
   ```

2. 修改生成所有数据方法，支持为所有企业ID生成数据：
   ```java
   public void generateAllMonitorData(int days) {
       for (String corpId : ALL_CORP_IDS) {
           generateWelcomeDataForCorpId(corpId, days);
           generateAiChatDataForCorpId(corpId, days);
           // ... 其他数据生成
       }
   }
   ```

3. 添加指定企业ID的数据生成方法：
   - `generateWelcomeDataForCorpId(String corpId, int days)`
   - `generateAiChatDataForCorpId(String corpId, int days)`
   - `generateMessageProcessDataForCorpId(String corpId, int days)`
   - `generateUserSatisfactionDataForCorpId(String corpId, int days)`
   - `generateExternalServiceDataForCorpId(String corpId, int days)`

### 3. 添加新的API接口

**文件**: `service/src/main/java/com/bj58/hy/wx/qywxbiz/interfaces/web/MonitorDataController.java`

```java
@PostMapping("/generate/corp/{corpId}")
public Result<String> generateDataForCorpId(
        @PathVariable String corpId,
        @RequestParam(defaultValue = "7") int days) {
    monitorDataGenerator.generateAllMonitorDataForCorpId(corpId, days);
    return Result.success("企业ID " + corpId + " 的监控数据生成成功");
}
```

## 验证步骤

### 1. 生成测试数据

```bash
# 为精选客服企业ID生成数据
curl -X POST "http://localhost:8080/monitor/data/generate/corp/ww5cfa32107e9a1f20?days=7"

# 为所有企业ID生成数据
curl -X POST "http://localhost:8080/monitor/data/generate?days=7"
```

### 2. 验证数据查询

```bash
# 测试概览数据
curl "http://localhost:8080/monitor/data/test-overview"

# 测试AI数据查询
curl "http://localhost:8080/monitor/data/test-ai-data"
```

### 3. 访问页面

访问以下URL验证页面显示：
- `http://localhost:8080/monitor/view/ai-chat?corpId=ww5cfa32107e9a1f20&days=3`
- `http://localhost:8080/monitor/view/overview?corpId=ww5cfa32107e9a1f20&days=3`

## 预期结果

修复后，AI聊天监控页面应该能够：

1. **正确显示数据**：页面不再空白，显示AI聊天相关的监控指标
2. **企业ID匹配**：下拉框选项与实际使用的企业ID一致
3. **数据完整性**：所有图表和指标都有对应的数据显示
4. **多企业支持**：支持为不同企业ID生成和查看监控数据

## 技术细节

### Redis数据存储格式

AI聊天数据在Redis中的存储key格式：
```
ai:call:success:{corpId}:sync:{date}     # 同步调用成功数
ai:call:failure:{corpId}:sync:{date}     # 同步调用失败数
ai:call:success:{corpId}:async:{date}    # 异步调用成功数
ai:call:failure:{corpId}:async:{date}    # 异步调用失败数
ai:result:type:{corpId}:{type}:{date}    # AI结果类型分布
ai:takeover:success:{corpId}:{date}      # AI承接成功数
ai:takeover:failure:{corpId}:{date}      # AI承接失败数
```

### 页面数据绑定

页面模板期望的数据结构：
```javascript
aiChatData: {
    syncSuccessRate: 95.73,
    syncSuccessData: {"2025-07-25": 791, "2025-07-26": 686, ...},
    syncFailureData: {"2025-07-25": 47, "2025-07-26": 21, ...},
    asyncSuccessRate: 94.81,
    asyncSuccessData: {...},
    asyncFailureData: {...},
    resultTypeData: {
        type_0: {...}, type_1: {...}, type_3: {...}, type_5: {...}
    },
    takeoverSuccessRate: 87.49,
    responseTimeDetails: [...]
}
```
