#!/bin/bash

# 快速修复测试脚本 - 验证Thymeleaf模板修复

echo "=== 监控页面Thymeleaf模板修复验证 ==="
echo "时间: $(date)"
echo "修复内容: 添加空值检查，防止模板渲染异常"
echo ""

BASE_URL="http://localhost:8080"
CORP_ID="ww5cfa32107e9a1f20"

echo "1. 检查服务状态..."
if curl -s --connect-timeout 5 "$BASE_URL/monitor/data/test-json" > /dev/null; then
    echo "✓ 服务运行正常"
else
    echo "✗ 服务未启动或无法连接"
    echo "请确保应用已启动并监听8080端口"
    exit 1
fi

echo ""
echo "2. 生成测试数据..."
echo "为企业ID $CORP_ID 生成监控数据..."

# 生成数据
GENERATE_RESULT=$(curl -s -X POST "$BASE_URL/monitor/data/generate/corp/$CORP_ID?days=7")
echo "生成结果: $GENERATE_RESULT"

# 等待数据生成完成
echo "等待数据生成完成..."
sleep 3

echo ""
echo "3. 测试页面访问（修复后）..."

echo "测试页面列表："
echo "🧪 测试页面: $BASE_URL/monitor/view/test?corpId=$CORP_ID&days=3"
echo "🔍 调试页面: $BASE_URL/monitor/view/debug?corpId=$CORP_ID&days=3"
echo "📊 监控概览: $BASE_URL/monitor/view/overview?corpId=$CORP_ID&days=3"
echo "🤖 AI聊天监控: $BASE_URL/monitor/view/ai-chat?corpId=$CORP_ID&days=3"

echo ""
echo "4. 验证API数据..."

# 测试概览API
echo "测试概览API..."
OVERVIEW_API=$(curl -s "$BASE_URL/monitor/api/overview?corpId=$CORP_ID&days=3")
if echo "$OVERVIEW_API" | grep -q '"code":0'; then
    echo "✓ 概览API返回成功"
else
    echo "✗ 概览API返回异常"
    echo "返回内容: $(echo "$OVERVIEW_API" | head -c 200)..."
fi

# 测试AI聊天API
echo ""
echo "测试AI聊天API..."
AI_CHAT_API=$(curl -s "$BASE_URL/monitor/api/ai-chat?corpId=$CORP_ID&days=3")
if echo "$AI_CHAT_API" | grep -q '"code":0'; then
    echo "✓ AI聊天API返回成功"
else
    echo "✗ AI聊天API返回异常"
    echo "返回内容: $(echo "$AI_CHAT_API" | head -c 200)..."
fi

echo ""
echo "5. 检查关键修复点..."

# 检查数据结构
if command -v jq &> /dev/null; then
    echo "使用jq检查数据结构..."
    
    # 检查概览数据结构
    OVERVIEW_KEYS=$(echo "$OVERVIEW_API" | jq -r '.data | keys[]' 2>/dev/null)
    if [ -n "$OVERVIEW_KEYS" ]; then
        echo "✓ 概览数据包含模块: $OVERVIEW_KEYS"
    else
        echo "✗ 概览数据结构异常"
    fi
    
    # 检查AI聊天数据
    AI_SYNC_RATE=$(echo "$AI_CHAT_API" | jq '.data.syncSuccessRate' 2>/dev/null)
    if [ "$AI_SYNC_RATE" != "null" ] && [ "$AI_SYNC_RATE" != "" ]; then
        echo "✓ AI同步成功率: $AI_SYNC_RATE%"
    else
        echo "✗ AI同步成功率数据缺失"
    fi
else
    echo "jq未安装，跳过JSON解析验证"
fi

echo ""
echo "6. 修复验证总结..."
echo ""
echo "已修复的问题："
echo "✅ Thymeleaf模板空值检查 - 防止访问null对象属性"
echo "✅ SpringEL表达式优化 - 修复复杂的列表/映射操作"
echo "✅ 企业ID统一 - 确保数据生成和查询使用相同ID"
echo "✅ 错误处理增强 - 添加调试信息和错误页面"
echo ""

echo "预期修复效果："
echo "• 页面不再因为空数据导致Thymeleaf异常"
echo "• 即使数据为空，页面也能正常显示（显示N/A）"
echo "• JavaScript图表能正常处理空数据"
echo "• 调试页面提供详细的数据状态信息"
echo ""

echo "下一步验证："
echo "1. 在浏览器中访问测试页面，确认页面框架正常"
echo "2. 访问调试页面，查看数据加载状态"
echo "3. 访问监控概览页面，确认数据正常显示"
echo "4. 检查浏览器控制台，确认无JavaScript错误"
echo ""

echo "如果页面仍有问题，请检查："
echo "• 应用日志中的异常信息"
echo "• 浏览器控制台的错误信息"
echo "• Redis服务是否正常运行"
echo "• 企业ID参数是否正确"
echo ""

echo "=== 修复验证完成 ==="
