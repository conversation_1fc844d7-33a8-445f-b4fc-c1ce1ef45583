#!/bin/bash

# 快速调试脚本 - 生成数据并验证页面

echo "=== 快速调试脚本 ==="
echo "时间: $(date)"

BASE_URL="http://localhost:8080"
CORP_ID="ww5cfa32107e9a1f20"

echo ""
echo "1. 检查服务状态..."
if curl -s --connect-timeout 5 "$BASE_URL/monitor/data/test-json" > /dev/null; then
    echo "✓ 服务运行正常"
else
    echo "✗ 服务未启动或无法连接"
    echo "请确保应用已启动并监听8080端口"
    exit 1
fi

echo ""
echo "2. 生成测试数据..."
echo "为企业ID $CORP_ID 生成7天的监控数据..."

# 生成数据
GENERATE_RESULT=$(curl -s -X POST "$BASE_URL/monitor/data/generate/corp/$CORP_ID?days=7")
echo "生成结果: $GENERATE_RESULT"

# 等待数据生成完成
echo "等待数据生成完成..."
sleep 3

echo ""
echo "3. 验证API数据..."

# 测试概览API
echo "测试概览API..."
OVERVIEW_API=$(curl -s "$BASE_URL/monitor/api/overview?corpId=$CORP_ID&days=3")
echo "概览API返回: $(echo "$OVERVIEW_API" | head -c 200)..."

# 测试AI聊天API
echo ""
echo "测试AI聊天API..."
AI_CHAT_API=$(curl -s "$BASE_URL/monitor/api/ai-chat?corpId=$CORP_ID&days=3")
echo "AI聊天API返回: $(echo "$AI_CHAT_API" | head -c 200)..."

echo ""
echo "4. 检查数据结构..."
# 使用jq检查数据结构（如果可用）
if command -v jq &> /dev/null; then
    echo "使用jq解析数据结构..."
    
    # 检查概览数据
    echo "概览数据结构:"
    echo "$OVERVIEW_API" | jq '.data | keys' 2>/dev/null || echo "JSON解析失败"
    
    # 检查AI聊天数据
    echo ""
    echo "AI聊天数据结构:"
    echo "$AI_CHAT_API" | jq '.data | keys' 2>/dev/null || echo "JSON解析失败"
    
    # 检查具体数值
    echo ""
    echo "关键指标:"
    echo "AI同步成功率: $(echo "$AI_CHAT_API" | jq '.data.syncSuccessRate' 2>/dev/null || echo "N/A")"
    echo "AI异步成功率: $(echo "$AI_CHAT_API" | jq '.data.asyncSuccessRate' 2>/dev/null || echo "N/A")"
else
    echo "jq未安装，跳过JSON解析"
fi

echo ""
echo "5. 访问页面进行验证..."
echo ""
echo "请在浏览器中访问以下页面："
echo ""
echo "🔍 调试页面（推荐先访问）:"
echo "   $BASE_URL/monitor/view/debug?corpId=$CORP_ID&days=3"
echo ""
echo "📊 监控概览页面:"
echo "   $BASE_URL/monitor/view/overview?corpId=$CORP_ID&days=3"
echo ""
echo "🤖 AI聊天监控页面:"
echo "   $BASE_URL/monitor/view/ai-chat?corpId=$CORP_ID&days=3"
echo ""

echo "6. 故障排查建议..."
echo ""
echo "如果页面仍然空白，请检查："
echo "1. 浏览器开发者工具 -> 控制台，查看JavaScript错误"
echo "2. 浏览器开发者工具 -> 网络，查看API请求是否成功"
echo "3. 应用日志，查看后端是否有异常"
echo "4. Redis服务是否正常运行"
echo ""

echo "7. 手动验证命令..."
echo ""
echo "手动测试API:"
echo "curl \"$BASE_URL/monitor/api/overview?corpId=$CORP_ID&days=3\""
echo ""
echo "手动生成数据:"
echo "curl -X POST \"$BASE_URL/monitor/data/generate/corp/$CORP_ID?days=7\""
echo ""

echo "=== 调试脚本完成 ==="
