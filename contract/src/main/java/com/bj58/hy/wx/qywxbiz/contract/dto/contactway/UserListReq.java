package com.bj58.hy.wx.qywxbiz.contract.dto.contactway;

import com.bj58.spat.scf.serializer.component.annotation.SCFMember;
import com.bj58.spat.scf.serializer.component.annotation.SCFSerializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SCFSerializable
public class UserListReq {

    /**
     * 企业微信唯一标识
     */
    @NotEmpty
    @SCFMember(orderId = 1)
    private String corpId;

    /**
     * 业务线
     */
    @javax.validation.constraints.NotNull
    @SCFMember(orderId = 2)
    private Integer bizLine;

    /**
     * 业务场景
     */
    @NotEmpty
    @SCFMember(orderId = 3)
    private List<Integer> bizScene;

    /**
     * 城市id
     */
    @SCFMember(orderId = 4)
    private Integer cityId;

    /**
     * 企微账户新增时间筛选-开始时间
     */
    @SCFMember(orderId = 5)
    private Date createStartTime;

    /**
     * 企微账户新增时间筛选-结束时间
     */
    @SCFMember(orderId = 6)
    private Date createEndTime;

    /**
     * 状态筛选项：选项为：不传为全部、0-待上线、1-已上线;
     */
    @SCFMember(orderId = 7)
    private Integer state;

    /**
     * 首次添加客户时间筛选-开始时间
     */
    @SCFMember(orderId = 8)
    private Date addCustomerStartTime;

    /**
     * 首次添加客户时间筛选-结束时间
     */
    @SCFMember(orderId = 9)
    private Date addCustomerEndTime;

    /**
     * 页码
     */
    @SCFMember(orderId = 10)
    private Integer pageNum;

    /**
     * 页大小
     */
    @SCFMember(orderId = 11)
    private Integer pageSize;

    /**
     * 更新时间筛选-开始时间
     */
    @SCFMember(orderId = 12)
    private Date updateStartTime;

    /**
     * 更新时间筛选-结束时间
     */
    @SCFMember(orderId = 13)
    private Date updateEndTime;

    /**
     * 企微userId
     */
    @SCFMember(orderId = 14)
    private String userId;

}
